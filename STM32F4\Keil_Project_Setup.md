# STM32F4竞赛级模块库 - Keil项目配置指南

## 📋 概述

本文档提供STM32F407VGT6竞赛级模块库在Keil MDK环境下的完整配置指南，确保项目能够正确编译和运行。

## 🎯 硬件平台

- **开发板**: 嘉立创"天空星"STM32F407VGT6
- **开发环境**: Keil MDK 5.x
- **软件库**: STM32F4标准外设库(SPL)
- **系统时钟**: 168MHz

## 📁 项目文件结构

```
STM32F4_Contest_Project/
├── User/
│   ├── main.c
│   ├── main.h
│   └── stm32f4xx_conf.h
├── Modules/
│   ├── Core/
│   │   ├── systick.c/h
│   │   └── usart.c/h
│   ├── Acquisition/
│   │   ├── adc_dma.c/h
│   │   └── parallel_adc.c/h
│   ├── Generation/
│   │   └── dds_wavegen.c/h
│   ├── Processing/
│   │   └── fft.c/h
│   └── Interface/
│       ├── oled.c/h
│       └── key.c/h
├── Libraries/
│   ├── STM32F4xx_StdPeriph_Driver/
│   ├── CMSIS/
│   └── startup_stm32f40_41xxx.s
└── Project/
    └── STM32F407.uvprojx
```

## ⚙️ Keil项目配置步骤

### 1. 创建新项目

1. 打开Keil MDK
2. 选择 `Project` -> `New µVision Project`
3. 选择目标器件：`STMicroelectronics` -> `STM32F407VGTx`
4. 在Manage Run-Time Environment中选择：
   - `CMSIS` -> `CORE`
   - `Device` -> `Startup`

### 2. 添加文件组和源文件

在Project窗口中添加以下文件组：

#### User组
- `User/main.c`
- `User/stm32f4xx_conf.h`

#### Modules组
- `Modules/Core/systick.c`
- `Modules/Core/usart.c`
- `Modules/Acquisition/adc_dma.c`
- `Modules/Acquisition/parallel_adc.c`
- `Modules/Generation/dds_wavegen.c`
- `Modules/Processing/fft.c`
- `Modules/Interface/oled.c`
- `Modules/Interface/key.c`

#### Libraries组
- `Libraries/STM32F4xx_StdPeriph_Driver/src/*.c` (所有.c文件)
- `Libraries/CMSIS/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c`

#### Startup组
- `startup_stm32f40_41xxx.s`

### 3. 配置项目选项

#### Target选项卡
- **Device**: STM32F407VGTx
- **Crystal (MHz)**: 25.0 (外部晶振)
- **Floating Point Hardware**: Use FPU (重要！FFT模块需要)

#### Output选项卡
- **Name of Executable**: STM32F407_Contest
- **Create HEX File**: ✓ 勾选

#### Listing选项卡
- **Assembler Listing**: ✓ 勾选

#### C/C++选项卡

**Include Paths** (关键配置):
```
..\User
..\Modules\Core
..\Modules\Acquisition
..\Modules\Generation
..\Modules\Processing
..\Modules\Interface
..\Libraries\STM32F4xx_StdPeriph_Driver\inc
..\Libraries\CMSIS\Device\ST\STM32F4xx\Include
..\Libraries\CMSIS\Include
..\Libraries\CMSIS\DSP\Include
```

**Define** (关键配置):
```
STM32F40_41xxx,USE_STDPERIPH_DRIVER,ARM_MATH_CM4,__FPU_PRESENT=1
```

**Optimization**: Level 2 (-O2)

#### Linker选项卡
- **Use Memory Layout from Target Dialog**: ✓ 勾选

#### Debug选项卡
- **Use**: ST-Link Debugger
- **Settings** -> **Flash Download**:
  - **Reset and Run**: ✓ 勾选

### 4. CMSIS-DSP库配置 (FFT模块专用)

#### 4.1 添加库文件
在Linker选项卡中，添加库文件：
```
..\Libraries\CMSIS\DSP\Lib\ARM\arm_cortexM4lf_math.lib
```

#### 4.2 验证FPU配置
确保以下配置正确：
- Target选项卡中，Floating Point Hardware选择 `Use FPU`
- Define中包含 `__FPU_PRESENT=1`

#### 4.3 测试FFT功能
在main.c中添加测试代码：
```c
#ifdef ARM_MATH_CM4
    // FFT功能可用
    FFT_Config_t fft_config = {
        .fft_size = FFT_SIZE_1024,
        .sample_rate = 100000
    };
    FFT_Init(&fft_config);
#else
    #error "ARM_MATH_CM4 not defined! Check project configuration."
#endif
```

## 🔧 编译和调试

### 编译检查清单
- [ ] 所有源文件已添加到项目
- [ ] Include路径配置正确
- [ ] 宏定义配置正确
- [ ] FPU配置正确(FFT模块)
- [ ] 库文件路径正确

### 常见编译错误及解决方案

#### 错误1: "arm_math.h: No such file or directory"
**解决方案**: 检查Include Paths中是否包含 `..\Libraries\CMSIS\DSP\Include`

#### 错误2: "undefined reference to arm_rfft_fast_init_f32"
**解决方案**: 
1. 检查是否添加了 `arm_cortexM4lf_math.lib`
2. 确认Define中包含 `ARM_MATH_CM4`

#### 错误3: "STM32F40_41xxx not defined"
**解决方案**: 在Define中添加 `STM32F40_41xxx,USE_STDPERIPH_DRIVER`

#### 错误4: I2C相关编译错误
**解决方案**: 确保包含了完整的STM32F4xx标准外设库

### 下载和调试配置

#### ST-Link配置
1. 连接ST-Link到开发板
2. 在Debug选项卡中选择ST-Link Debugger
3. 点击Settings，确认能检测到目标器件
4. 在Flash Download中勾选"Reset and Run"

#### 串口调试配置
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控**: 无

## 📊 性能优化建议

### 编译优化
- 使用 `-O2` 优化级别
- 启用 `Use FPU` 提升浮点运算性能
- 合理配置堆栈大小

### 内存优化
- 合理配置各模块缓冲区大小
- 使用DMA减少CPU占用
- 避免在中断中进行复杂计算

### 调试优化
- 使用串口输出调试信息
- 合理设置断点
- 监控关键变量

## 🚨 注意事项

1. **时钟配置**: 确保系统时钟配置为168MHz
2. **中断优先级**: DMA中断设置为最高优先级
3. **FPU配置**: FFT模块必须启用FPU
4. **内存对齐**: 某些CMSIS-DSP函数要求数据对齐
5. **堆栈大小**: 根据实际使用情况调整堆栈大小

## 📞 故障排除

如果遇到问题，请按以下顺序检查：

1. **硬件连接**: 确认所有硬件连接正确
2. **项目配置**: 对照本文档检查所有配置项
3. **库文件**: 确认所有必需的库文件已正确添加
4. **宏定义**: 检查所有必需的宏定义
5. **编译输出**: 仔细阅读编译错误信息

---

**版权所有 © 2024 嵌入式竞赛团队**

**最后更新**: 2024年 - 黄金参考锁定版本
