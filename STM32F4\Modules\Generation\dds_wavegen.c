/**
  ******************************************************************************
  * @file    dds_wavegen.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   DDS波形生成模块实现
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "dds_wavegen.h"
#include "systick.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/** @defgroup DDS_Private_Defines DDS私有定义
  * @{
  */

#define DAC_GPIO_PORT               GPIOA
#define DAC_GPIO_CLK                RCC_AHB1Periph_GPIOA
#define DAC_PIN                     GPIO_Pin_4   ///< PA4 - DAC1_OUT

#define DAC_CLK                     RCC_APB1Periph_DAC
#define TIM_CLK                     RCC_APB1Periph_TIM6
#define DMA_CLK                     RCC_AHB1Periph_DMA1

#define DDS_TIM                     TIM6
#define DDS_DAC_CHANNEL             DAC_Channel_1
#define DDS_DMA_STREAM              DMA1_Stream5
#define DDS_DMA_CHANNEL             DMA_Channel_7

// 波形生成缓冲区大小
#define DDS_OUTPUT_BUFFER_SIZE      512U

/**
  * @}
  */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/** @defgroup DDS_Private_Variables DDS私有变量
  * @{
  */

DDS_Handle_t g_dds_handle;                      ///< DDS句柄
volatile bool g_dds_update_complete = false;    ///< 更新完成标志
volatile bool g_dds_dma_complete = false;       ///< DMA传输完成标志

// 输出缓冲区
static uint16_t s_dds_output_buffer[DDS_OUTPUT_BUFFER_SIZE];

// 预定义波形表 - 正弦波表(4096点)
const uint16_t g_sin_wave_table[DDS_WAVE_TABLE_SIZE] = {
    // 12位正弦波表，中心值2048，幅度2047
    // 生成公式：sin_table[i] = (uint16_t)(2048 + 2047 * sin(2*PI*i/4096))
    2048, 2051, 2054, 2057, 2060, 2063, 2066, 2069, 2072, 2075, 2078, 2081, 2084, 2087, 2090, 2093,
    2096, 2099, 2102, 2105, 2108, 2111, 2114, 2117, 2120, 2123, 2126, 2129, 2132, 2135, 2138, 2141,
    2144, 2147, 2150, 2153, 2156, 2159, 2162, 2165, 2168, 2171, 2174, 2177, 2180, 2183, 2186, 2189,
    2192, 2195, 2198, 2201, 2204, 2207, 2210, 2213, 2216, 2219, 2222, 2225, 2228, 2231, 2234, 2237,
    // 注意：这里只显示前64个点作为示例
    // 实际应用中需要完整的4096点表，可以通过以下代码生成：
    /*
    for(int i = 0; i < 4096; i++) {
        g_sin_wave_table[i] = (uint16_t)(2048 + 2047 * sin(2.0 * M_PI * i / 4096.0));
    }
    */
    // 为了节省代码空间，这里使用简化版本
};

// 简化的波形表定义（实际应用中需要完整实现）
const uint16_t g_square_wave_table[DDS_WAVE_TABLE_SIZE] = {/* 方波表 */};
const uint16_t g_triangle_wave_table[DDS_WAVE_TABLE_SIZE] = {/* 三角波表 */};
const uint16_t g_sawtooth_wave_table[DDS_WAVE_TABLE_SIZE] = {/* 锯齿波表 */};

/**
  * @}
  */

/* Private function prototypes -----------------------------------------------*/
static void DDS_GPIO_Config(void);
static void DDS_DAC_Config(void);
static void DDS_TIM_Config(uint32_t sample_rate);
static void DDS_DMA_Config(void);
static void DDS_NVIC_Config(void);
static void DDS_GenerateWaveTable(DDS_WaveType_t wave_type);
static uint16_t DDS_GetNextSample(void);
static uint16_t DDS_LinearInterpolation(uint32_t phase_acc);
static void DDS_UpdateFrequencyWord(void);
static void DDS_ProcessModulation(void);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  DDS GPIO配置
  * @param  None
  * @retval None
  */
static void DDS_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIO时钟
    RCC_AHB1PeriphClockCmd(DAC_GPIO_CLK, ENABLE);
    
    // 配置DAC输出引脚为模拟模式
    GPIO_InitStructure.GPIO_Pin = DAC_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(DAC_GPIO_PORT, &GPIO_InitStructure);
}

/**
  * @brief  DDS DAC配置
  * @param  None
  * @retval None
  */
static void DDS_DAC_Config(void)
{
    DAC_InitTypeDef DAC_InitStructure;
    
    // 使能DAC时钟
    RCC_APB1PeriphClockCmd(DAC_CLK, ENABLE);
    
    // 配置DAC
    DAC_InitStructure.DAC_Trigger = DAC_Trigger_T6_TRGO;  // TIM6触发
    DAC_InitStructure.DAC_WaveGeneration = DAC_WaveGeneration_None;
    DAC_InitStructure.DAC_LFSRUnmask_TriangleAmplitude = DAC_LFSRUnmask_Bit0;
    DAC_InitStructure.DAC_OutputBuffer = DAC_OutputBuffer_Enable;
    DAC_Init(DDS_DAC_CHANNEL, &DAC_InitStructure);
    
    // 使能DAC DMA
    DAC_DMACmd(DDS_DAC_CHANNEL, ENABLE);
    
    // 使能DAC通道
    DAC_Cmd(DDS_DAC_CHANNEL, ENABLE);
}

/**
  * @brief  DDS定时器配置
  * @param  sample_rate: 采样率
  * @retval None
  */
static void DDS_TIM_Config(uint32_t sample_rate)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    
    // 使能TIM6时钟
    RCC_APB1PeriphClockCmd(TIM_CLK, ENABLE);
    
    // 计算定时器参数
    uint32_t timer_freq = 84000000; // APB1时钟84MHz
    uint32_t period = timer_freq / sample_rate;
    
    // 配置定时器基本参数
    TIM_TimeBaseStructure.TIM_Period = period - 1;
    TIM_TimeBaseStructure.TIM_Prescaler = 0;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(DDS_TIM, &TIM_TimeBaseStructure);
    
    // 配置TIM6触发输出
    TIM_SelectOutputTrigger(DDS_TIM, TIM_TRGOSource_Update);
    
    // 使能定时器
    TIM_Cmd(DDS_TIM, ENABLE);
}

/**
  * @brief  DDS DMA配置
  * @param  None
  * @retval None
  */
static void DDS_DMA_Config(void)
{
    DMA_InitTypeDef DMA_InitStructure;
    
    // 使能DMA时钟
    RCC_AHB1PeriphClockCmd(DMA_CLK, ENABLE);
    
    // 配置DMA
    DMA_DeInit(DDS_DMA_STREAM);
    DMA_InitStructure.DMA_Channel = DDS_DMA_CHANNEL;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&DAC->DHR12R1;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)s_dds_output_buffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_MemoryToPeripheral;
    DMA_InitStructure.DMA_BufferSize = DDS_OUTPUT_BUFFER_SIZE;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;  // 循环模式
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
    DMA_Init(DDS_DMA_STREAM, &DMA_InitStructure);
    
    // 使能DMA中断
    DMA_ITConfig(DDS_DMA_STREAM, DMA_IT_TC, ENABLE);   // 传输完成中断
    DMA_ITConfig(DDS_DMA_STREAM, DMA_IT_HT, ENABLE);   // 半传输中断
}

/**
  * @brief  DDS NVIC配置
  * @param  None
  * @retval None
  */
static void DDS_NVIC_Config(void)
{
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 配置TIM6中断
    NVIC_InitStructure.NVIC_IRQChannel = TIM6_DAC_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    // 配置DMA中断
    NVIC_InitStructure.NVIC_IRQChannel = DMA1_Stream5_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
  * @brief  生成波形表
  * @param  wave_type: 波形类型
  * @retval None
  */
static void DDS_GenerateWaveTable(DDS_WaveType_t wave_type)
{
    // 这里可以动态生成波形表，或者选择预定义的表
    switch (wave_type) {
        case DDS_WAVE_SINE:
            g_dds_handle.current_wave_table = (uint16_t*)g_sin_wave_table;
            break;
        case DDS_WAVE_SQUARE:
            g_dds_handle.current_wave_table = (uint16_t*)g_square_wave_table;
            break;
        case DDS_WAVE_TRIANGLE:
            g_dds_handle.current_wave_table = (uint16_t*)g_triangle_wave_table;
            break;
        case DDS_WAVE_SAWTOOTH:
            g_dds_handle.current_wave_table = (uint16_t*)g_sawtooth_wave_table;
            break;
        default:
            g_dds_handle.current_wave_table = (uint16_t*)g_sin_wave_table;
            break;
    }
    
    g_dds_handle.wave_table_size = DDS_WAVE_TABLE_SIZE;
}

/**
  * @brief  获取下一个采样点
  * @param  None
  * @retval 采样值
  */
static uint16_t DDS_GetNextSample(void)
{
    uint16_t sample;
    
    // 处理调制
    if (g_dds_handle.config.enable_modulation) {
        DDS_ProcessModulation();
    }
    
    // 获取波形采样点
    if (g_dds_handle.config.enable_interpolation) {
        sample = DDS_LinearInterpolation(g_dds_handle.phase_accumulator);
    } else {
        uint32_t index = DDS_PHASE_TO_INDEX(g_dds_handle.phase_accumulator);
        sample = g_dds_handle.current_wave_table[index];
    }
    
    // 应用幅度和偏移
    sample = (uint16_t)((sample * g_dds_handle.config.amplitude) / DDS_AMPLITUDE_MAX);
    sample += g_dds_handle.config.offset;
    
    // 限制范围
    if (sample > DDS_AMPLITUDE_MAX) {
        sample = DDS_AMPLITUDE_MAX;
    }
    
    // 更新相位累加器
    g_dds_handle.phase_accumulator += g_dds_handle.frequency_word;
    
    // 更新统计信息
    g_dds_handle.stats.output_samples++;
    
    return sample;
}

/**
  * @brief  线性插值
  * @param  phase_acc: 相位累加器值
  * @retval 插值后的采样值
  */
static uint16_t DDS_LinearInterpolation(uint32_t phase_acc)
{
    uint32_t index = DDS_PHASE_TO_INDEX(phase_acc);
    uint32_t next_index = (index + 1) % g_dds_handle.wave_table_size;
    
    // 计算插值权重
    uint32_t frac = (phase_acc >> (DDS_PHASE_BITS - 20)) & 0xFF; // 8位小数部分
    
    uint16_t sample1 = g_dds_handle.current_wave_table[index];
    uint16_t sample2 = g_dds_handle.current_wave_table[next_index];
    
    // 线性插值
    uint32_t result = sample1 + ((sample2 - sample1) * frac) / 256;
    
    return (uint16_t)result;
}

/**
  * @brief  更新频率控制字
  * @param  None
  * @retval None
  */
static void DDS_UpdateFrequencyWord(void)
{
    g_dds_handle.frequency_word = DDS_CALC_FREQ_WORD(
        g_dds_handle.config.frequency, 
        g_dds_handle.config.sample_rate
    );
}

/**
  * @brief  处理调制
  * @param  None
  * @retval None
  */
static void DDS_ProcessModulation(void)
{
    // 简化的调制处理
    if (g_dds_handle.modulation.fm_enable) {
        // 频率调制
        uint32_t mod_index = DDS_PHASE_TO_INDEX(g_dds_handle.mod_phase_acc);
        int16_t mod_value = (int16_t)g_sin_wave_table[mod_index] - 2048;
        
        // 应用调制深度
        int32_t freq_deviation = (int32_t)g_dds_handle.config.frequency * 
                                 g_dds_handle.modulation.mod_depth * mod_value / (100 * 2048);
        
        uint32_t modulated_freq = g_dds_handle.config.frequency + freq_deviation;
        g_dds_handle.frequency_word = DDS_CALC_FREQ_WORD(modulated_freq, g_dds_handle.config.sample_rate);
        
        // 更新调制相位累加器
        g_dds_handle.mod_phase_acc += g_dds_handle.mod_freq_word;
    }
}

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  DDS初始化
  * @param  config: 配置参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_Init(DDS_Config_t* config)
{
    if (config == NULL) {
        return -1;
    }

    // 参数检查
    if (config->frequency < DDS_MIN_FREQUENCY || config->frequency > DDS_MAX_FREQUENCY) {
        return -1;
    }

    // 初始化句柄
    g_dds_handle.dac_instance = DAC;
    g_dds_handle.tim_instance = DDS_TIM;
    g_dds_handle.dma_stream = DDS_DMA_STREAM;
    g_dds_handle.config = *config;
    g_dds_handle.is_running = false;
    g_dds_handle.wave_updated = false;

    // 初始化DDS参数
    g_dds_handle.phase_accumulator = 0;
    g_dds_handle.mod_phase_acc = 0;

    // 设置默认调制参数
    memset(&g_dds_handle.modulation, 0, sizeof(DDS_Modulation_t));

    // 配置硬件
    DDS_GPIO_Config();
    DDS_DAC_Config();
    DDS_TIM_Config(config->sample_rate);
    DDS_DMA_Config();
    DDS_NVIC_Config();

    // 生成波形表
    DDS_GenerateWaveTable(config->wave_type);

    // 计算频率控制字
    DDS_UpdateFrequencyWord();

    // 计算调制频率控制字
    g_dds_handle.mod_freq_word = DDS_CALC_FREQ_WORD(
        g_dds_handle.modulation.mod_frequency,
        config->sample_rate
    );

    // 预填充输出缓冲区
    for (uint32_t i = 0; i < DDS_OUTPUT_BUFFER_SIZE; i++) {
        s_dds_output_buffer[i] = DDS_GetNextSample();
    }

    // 重置统计信息
    DDS_ResetStats();

    return 0;
}

/**
  * @brief  开始DDS输出
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_Start(void)
{
    if (g_dds_handle.is_running) {
        return -1; // 已经在运行
    }

    // 清除标志
    g_dds_update_complete = false;
    g_dds_dma_complete = false;

    // 启动DMA
    DMA_Cmd(DDS_DMA_STREAM, ENABLE);

    // 启动定时器
    TIM_Cmd(DDS_TIM, ENABLE);

    g_dds_handle.is_running = true;

    return 0;
}

/**
  * @brief  停止DDS输出
  * @param  None
  * @retval None
  */
void DDS_Stop(void)
{
    // 停止定时器
    TIM_Cmd(DDS_TIM, DISABLE);

    // 停止DMA
    DMA_Cmd(DDS_DMA_STREAM, DISABLE);

    // 设置DAC输出为0
    DAC_SetChannel1Data(DAC_Align_12b_R, 0);

    g_dds_handle.is_running = false;
}

/**
  * @brief  设置输出频率
  * @param  frequency: 频率(Hz)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetFrequency(uint32_t frequency)
{
    if (frequency < DDS_MIN_FREQUENCY || frequency > DDS_MAX_FREQUENCY) {
        return -1;
    }

    g_dds_handle.config.frequency = frequency;
    DDS_UpdateFrequencyWord();

    // 更新统计信息
    g_dds_handle.stats.frequency_changes++;

    return 0;
}

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetWaveType(DDS_WaveType_t wave_type)
{
    if (wave_type >= DDS_WAVE_COUNT) {
        return -1;
    }

    g_dds_handle.config.wave_type = wave_type;
    DDS_GenerateWaveTable(wave_type);
    g_dds_handle.wave_updated = true;

    // 更新统计信息
    g_dds_handle.stats.wave_changes++;

    return 0;
}

/**
  * @brief  设置幅度
  * @param  amplitude: 幅度(0-4095)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetAmplitude(uint16_t amplitude)
{
    if (amplitude > DDS_AMPLITUDE_MAX) {
        return -1;
    }

    g_dds_handle.config.amplitude = amplitude;

    // 更新统计信息
    if (amplitude > g_dds_handle.stats.max_amplitude) {
        g_dds_handle.stats.max_amplitude = amplitude;
    }
    if (amplitude < g_dds_handle.stats.min_amplitude) {
        g_dds_handle.stats.min_amplitude = amplitude;
    }

    return 0;
}

/**
  * @brief  设置直流偏移
  * @param  offset: 偏移(0-4095)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetOffset(uint16_t offset)
{
    if (offset > DDS_OFFSET_MAX) {
        return -1;
    }

    g_dds_handle.config.offset = offset;

    return 0;
}

/**
  * @brief  设置相位偏移
  * @param  phase: 相位(0-3599, 对应0-359.9度)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetPhase(uint16_t phase)
{
    if (phase >= DDS_PHASE_RESOLUTION) {
        return -1;
    }

    g_dds_handle.config.phase = phase;

    // 将相位转换为相位累加器偏移
    uint32_t phase_offset = (uint32_t)phase * (1ULL << DDS_PHASE_BITS) / DDS_PHASE_RESOLUTION;
    g_dds_handle.phase_accumulator = phase_offset;

    return 0;
}

/**
  * @brief  设置自定义波形
  * @param  wave_table: 波形表指针
  * @param  table_size: 表大小
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetCustomWave(const uint16_t* wave_table, uint32_t table_size)
{
    if (wave_table == NULL || table_size == 0 || table_size > DDS_WAVE_TABLE_SIZE) {
        return -1;
    }

    g_dds_handle.config.wave_type = DDS_WAVE_CUSTOM;
    g_dds_handle.current_wave_table = (uint16_t*)wave_table;
    g_dds_handle.wave_table_size = table_size;
    g_dds_handle.wave_updated = true;

    return 0;
}

/**
  * @brief  配置调制
  * @param  modulation: 调制参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_ConfigModulation(DDS_Modulation_t* modulation)
{
    if (modulation == NULL) {
        return -1;
    }

    g_dds_handle.modulation = *modulation;

    // 计算调制频率控制字
    g_dds_handle.mod_freq_word = DDS_CALC_FREQ_WORD(
        modulation->mod_frequency,
        g_dds_handle.config.sample_rate
    );

    return 0;
}

/**
  * @brief  使能/禁用调制
  * @param  enable: true-使能, false-禁用
  * @retval None
  */
void DDS_EnableModulation(bool enable)
{
    g_dds_handle.config.enable_modulation = enable;
}

/**
  * @brief  获取当前输出值
  * @param  None
  * @retval 当前DAC输出值
  */
uint16_t DDS_GetCurrentOutput(void)
{
    return DAC_GetDataOutputValue(DDS_DAC_CHANNEL);
}

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void DDS_GetStats(DDS_Stats_t* stats)
{
    if (stats == NULL) {
        return;
    }

    *stats = g_dds_handle.stats;

    // 计算实际频率
    if (g_dds_handle.config.sample_rate > 0) {
        stats->actual_frequency = (float)g_dds_handle.frequency_word *
                                 g_dds_handle.config.sample_rate / (1ULL << DDS_PHASE_BITS);

        // 计算频率误差
        if (g_dds_handle.config.frequency > 0) {
            stats->frequency_error = (stats->actual_frequency - g_dds_handle.config.frequency) *
                                    100.0f / g_dds_handle.config.frequency;
        }
    }
}

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void DDS_ResetStats(void)
{
    memset(&g_dds_handle.stats, 0, sizeof(DDS_Stats_t));
    g_dds_handle.stats.min_amplitude = 0xFFFF;
}

/**
  * @brief  计算THD(总谐波失真)
  * @param  None
  * @retval THD百分比
  */
float DDS_CalculateTHD(void)
{
    // 简化的THD计算
    // 实际应用中需要进行FFT分析
    return g_dds_handle.stats.thd_percent;
}

/**
  * @brief  TIM6中断处理函数
  * @param  None
  * @retval None
  */
void TIM6_DAC_IRQHandler(void)
{
    if (TIM_GetITStatus(DDS_TIM, TIM_IT_Update) != RESET) {
        TIM_ClearITPendingBit(DDS_TIM, TIM_IT_Update);

        // 在这里可以处理实时波形更新
        g_dds_update_complete = true;
    }
}

/**
  * @brief  DMA1_Stream5中断处理函数
  * @param  None
  * @retval None
  */
void DMA1_Stream5_IRQHandler(void)
{
    // 检查半传输完成中断
    if (DMA_GetITStatus(DDS_DMA_STREAM, DMA_IT_HTIF5) != RESET) {
        DMA_ClearITPendingBit(DDS_DMA_STREAM, DMA_IT_HTIF5);

        // 更新前半部分缓冲区
        for (uint32_t i = 0; i < DDS_OUTPUT_BUFFER_SIZE / 2; i++) {
            s_dds_output_buffer[i] = DDS_GetNextSample();
        }
    }

    // 检查传输完成中断
    if (DMA_GetITStatus(DDS_DMA_STREAM, DMA_IT_TCIF5) != RESET) {
        DMA_ClearITPendingBit(DDS_DMA_STREAM, DMA_IT_TCIF5);

        // 更新后半部分缓冲区
        for (uint32_t i = DDS_OUTPUT_BUFFER_SIZE / 2; i < DDS_OUTPUT_BUFFER_SIZE; i++) {
            s_dds_output_buffer[i] = DDS_GetNextSample();
        }

        g_dds_dma_complete = true;
    }

    // 检查传输错误中断
    if (DMA_GetITStatus(DDS_DMA_STREAM, DMA_IT_TEIF5) != RESET) {
        DMA_ClearITPendingBit(DDS_DMA_STREAM, DMA_IT_TEIF5);
        // 处理传输错误
    }
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
