/**
  ******************************************************************************
  * @file    systick.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   高精度SysTick延时模块 - 竞赛级实现
  *          支持微秒级精确延时、非阻塞延时、温度补偿等高级功能
  ******************************************************************************
  * @attention
  * 
  * 本模块特性：
  * 1. 使用DWT计数器实现微秒级精确延时
  * 2. 提供阻塞和非阻塞延时接口
  * 3. 支持延时校准和温度补偿
  * 4. 高精度时间戳获取
  * 5. 系统运行时间统计
  * 
  * 硬件要求：
  * - STM32F407VGT6 (Cortex-M4)
  * - 系统时钟168MHz
  * - DWT调试单元使能
  *
  ******************************************************************************
  */

#ifndef __SYSTICK_H
#define __SYSTICK_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  延时校准结构体
  */
typedef struct {
    float calibration_factor;    ///< 校准因子
    int16_t temperature_offset;  ///< 温度补偿偏移
    uint32_t last_calibration;   ///< 上次校准时间戳
} SysTick_Calibration_t;

/**
  * @brief  非阻塞延时句柄
  */
typedef struct {
    uint32_t start_time;         ///< 开始时间戳
    uint32_t delay_time;         ///< 延时时间(us)
    bool is_active;              ///< 延时是否激活
    bool is_completed;           ///< 延时是否完成
} SysTick_NonBlocking_t;

/**
  * @brief  系统时间统计结构体
  */
typedef struct {
    uint32_t system_uptime_ms;   ///< 系统运行时间(ms)
    uint32_t total_delay_us;     ///< 总延时时间(us)
    uint32_t delay_call_count;   ///< 延时调用次数
    float cpu_usage_percent;     ///< CPU使用率(%)
} SysTick_Stats_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup SysTick_Exported_Constants SysTick导出常量
  * @{
  */

#define SYSTICK_FREQUENCY_HZ        1000U    ///< SysTick中断频率(1ms)
#define DWT_CYCCNT_FREQUENCY_HZ     168000000U ///< DWT计数器频率(168MHz)
#define US_TO_DWT_CYCLES(us)        ((us) * (DWT_CYCCNT_FREQUENCY_HZ / 1000000U))
#define MS_TO_DWT_CYCLES(ms)        ((ms) * (DWT_CYCCNT_FREQUENCY_HZ / 1000U))

#define SYSTICK_MAX_DELAY_US        1000000U  ///< 最大延时时间(1秒)
#define SYSTICK_MIN_DELAY_US        1U        ///< 最小延时时间(1微秒)

#define SYSTICK_CALIBRATION_INTERVAL_MS  60000U  ///< 校准间隔(1分钟)

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup SysTick_Exported_Macros SysTick导出宏
  * @{
  */

/**
  * @brief  获取当前DWT计数器值
  */
#define GET_DWT_CYCCNT()            (DWT->CYCCNT)

/**
  * @brief  检查DWT是否使能
  */
#define IS_DWT_ENABLED()            ((CoreDebug->DEMCR & CoreDebug_DEMCR_TRCENA_Msk) && \
                                     (DWT->CTRL & DWT_CTRL_CYCCNTENA_Msk))

/**
  * @brief  微秒转换为系统滴答数
  */
#define US_TO_SYSTICKS(us)          ((us) / 1000U)

/**
  * @brief  毫秒转换为系统滴答数  
  */
#define MS_TO_SYSTICKS(ms)          (ms)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup SysTick_Exported_Variables SysTick导出变量
  * @{
  */

extern volatile uint32_t g_systick_counter;      ///< SysTick计数器
extern volatile uint32_t g_system_uptime_ms;    ///< 系统运行时间(ms)
extern SysTick_Calibration_t g_systick_cal;     ///< 延时校准参数
extern SysTick_Stats_t g_systick_stats;         ///< 系统时间统计

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup SysTick_Exported_Functions SysTick导出函数
  * @{
  */

/**
  * @brief  SysTick系统初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t SysTick_Init(void);

/**
  * @brief  DWT调试单元初始化
  * @param  None  
  * @retval 0: 成功, -1: 失败
  */
int8_t DWT_Init(void);

/**
  * @brief  微秒级精确延时(阻塞)
  * @param  us: 延时时间(微秒), 范围: 1 - 1000000
  * @retval None
  * @note   使用DWT计数器实现，精度±1us
  */
void Delay_us(uint32_t us);

/**
  * @brief  毫秒级延时(阻塞)
  * @param  ms: 延时时间(毫秒)
  * @retval None
  * @note   基于SysTick中断实现
  */
void Delay_ms(uint32_t ms);

/**
  * @brief  秒级延时(阻塞)
  * @param  s: 延时时间(秒)
  * @retval None
  */
void Delay_s(uint32_t s);

/**
  * @brief  非阻塞延时初始化
  * @param  handle: 非阻塞延时句柄
  * @param  delay_us: 延时时间(微秒)
  * @retval None
  */
void SysTick_NonBlocking_Init(SysTick_NonBlocking_t* handle, uint32_t delay_us);

/**
  * @brief  检查非阻塞延时是否完成
  * @param  handle: 非阻塞延时句柄
  * @retval true: 延时完成, false: 延时未完成
  */
bool SysTick_NonBlocking_IsCompleted(SysTick_NonBlocking_t* handle);

/**
  * @brief  获取高精度时间戳(微秒)
  * @param  None
  * @retval 当前时间戳(微秒)
  */
uint64_t SysTick_GetTimestamp_us(void);

/**
  * @brief  获取系统运行时间(毫秒)
  * @param  None
  * @retval 系统运行时间(毫秒)
  */
uint32_t SysTick_GetUptime_ms(void);

/**
  * @brief  获取系统滴答计数
  * @param  None
  * @retval 当前滴答计数
  */
uint32_t SysTick_GetTick(void);

/**
  * @brief  延时校准
  * @param  reference_delay_us: 参考延时时间(微秒)
  * @retval 校准因子
  */
float SysTick_Calibrate(uint32_t reference_delay_us);

/**
  * @brief  设置温度补偿
  * @param  temperature: 当前温度(°C * 10)
  * @retval None
  */
void SysTick_SetTemperatureCompensation(int16_t temperature);

/**
  * @brief  获取系统时间统计
  * @param  stats: 统计数据结构体指针
  * @retval None
  */
void SysTick_GetStats(SysTick_Stats_t* stats);

/**
  * @brief  重置系统时间统计
  * @param  None
  * @retval None
  */
void SysTick_ResetStats(void);

/**
  * @brief  SysTick中断处理函数
  * @param  None
  * @retval None
  * @note   在stm32f4xx_it.c中调用
  */
void SysTick_Handler(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __SYSTICK_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
