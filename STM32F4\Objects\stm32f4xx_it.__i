--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I .\User -I .\Start -I .\Library -I .\HardWare -I .\Modules
-I.\RTE\_Target_1
-I"D:\keil5   MDK\ARM\PACK\ARM\CMSIS\5.0.1\CMSIS\Include"
-I"D:\keil5   MDK\ARM\PACK\Keil\STM32F4xx_DFP\1.0.8\Device\Include"
-I"D:\keil5   MDK\ARM\CMSIS\Include"
-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F40_41xxx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER.ARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING
-o .\objects\stm32f4xx_it.o --omf_browse .\objects\stm32f4xx_it.crf --depend .\objects\stm32f4xx_it.d "User\stm32f4xx_it.c"