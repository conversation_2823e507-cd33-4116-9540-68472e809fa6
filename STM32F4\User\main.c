/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024
  * @brief   STM32F4竞赛级模块库集成示例 - 嘉立创天空星STM32F407VGT6
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>

// 竞赛级模块库
#include "systick.h"
#include "usart.h"
#include "adc_dma.h"
#include "parallel_adc.h"
#include "dds_wavegen.h"
// #include "fft.h"           // FFT模块(需要CMSIS-DSP库)
// #include "input_capture.h" // 频率测量模块
// #include "pwm.h"           // PWM生成模块
// #include "oled.h"          // OLED显示模块
// #include "key.h"           // 按键扫描模块


/* Private variables ---------------------------------------------------------*/
static char debug_buffer[256];

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void Module_Init_Demo(void);
void Module_Test_Demo(void);

/**
  * @brief  Main program - 竞赛级模块库集成示例
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统时钟初始化 */
    SystemInit();

    /* 初始化竞赛级模块库 */
    Module_Init_Demo();

    /* 模块功能测试演示 */
    Module_Test_Demo();

    /* 主循环 - 简化版本，专注于模块集成演示 */
    while (1)
    {
        /* 检查ADC数据是否就绪 */
        if (g_adc_dma_full_complete) {
            g_adc_dma_full_complete = false;

            // 处理ADC数据
            ADC_ProcessData();

            // 通过串口输出统计信息
            ADC_Stats_t adc_stats;
            ADC_GetStats(&adc_stats);

            sprintf(debug_buffer, "ADC: Samples=%lu, Rate=%.1fkSPS\r\n",
                    adc_stats.total_samples, adc_stats.actual_sample_rate/1000.0f);
            USART_SendString(debug_buffer);
        }

        /* 检查并行ADC数据 */
        if (g_parallel_adc_data_ready) {
            g_parallel_adc_data_ready = false;

            uint16_t data;
            if (ParallelADC_ReadSingle(&data) == 0) {
                sprintf(debug_buffer, "Parallel ADC: %d\r\n", data);
                USART_SendString(debug_buffer);
            }
        }

        /* 检查DDS更新 */
        if (g_dds_update_complete) {
            g_dds_update_complete = false;

            DDS_Stats_t dds_stats;
            DDS_GetStats(&dds_stats);

            sprintf(debug_buffer, "DDS: Freq=%.1fHz, Samples=%lu\r\n",
                    dds_stats.actual_frequency, dds_stats.output_samples);
            USART_SendString(debug_buffer);
        }

        /* 系统状态监控 */
        static uint32_t last_status_time = 0;
        if (SysTick_GetTick() - last_status_time > 5000) { // 每5秒输出一次状态
            last_status_time = SysTick_GetTick();

            sprintf(debug_buffer, "System Uptime: %lu ms\r\n", SysTick_GetUptime_ms());
            USART_SendString(debug_buffer);
        }

        /* 短暂延时，降低CPU占用 */
        Delay_ms(10);
    }
}

/**
  * @brief  竞赛级模块库初始化演示
  * @param  None
  * @retval None
  */
void Module_Init_Demo(void)
{
    /* 1. 初始化SysTick高精度延时模块 */
    if (SysTick_Init() != 0) {
        // 初始化失败处理
        while (1);
    }

    /* 2. 初始化USART调试模块 */
    if (USART1_Init(115200) != 0) {
        // 初始化失败处理
        while (1);
    }

    /* 输出启动信息 */
    USART_Printf("\r\n=== STM32F4竞赛级模块库演示 ===\r\n");
    USART_Printf("系统时钟: %lu MHz\r\n", SystemCoreClock / 1000000);
    USART_Printf("编译时间: %s %s\r\n", __DATE__, __TIME__);

    /* 3. 初始化ADC+DMA多通道采集模块 */
    ADC_Config_t adc_config = {
        .sample_rate = 100000,          // 100kSPS
        .resolution = ADC_RESOLUTION_12BIT,
        .oversample_ratio = 4,          // 4倍过采样
        .enable_filter = true,          // 使能数字滤波
        .enable_calibration = true,     // 使能自动校准
        .mode = ADC_MODE_CONTINUOUS     // 连续采样模式
    };

    if (ADC1_DMA_Init(&adc_config) == 0) {
        USART_Printf("ADC+DMA模块初始化成功\r\n");
        ADC_Start_Acquisition();
    } else {
        USART_Printf("ADC+DMA模块初始化失败\r\n");
    }

    /* 4. 初始化并行ADC接口模块 */
    ParallelADC_Config_t parallel_config = {
        .max_sample_rate = 10000000,    // 10MSPS
        .data_width = 12,               // 12位数据
        .enable_data_valid = false,     // 禁用数据有效信号
        .enable_overflow_detect = false, // 禁用溢出检测
        .enable_packing = true,         // 使能数据打包
        .trigger_edge = PARALLEL_ADC_TRIGGER_RISING // 上升沿触发
    };

    if (ParallelADC_Init(&parallel_config) == 0) {
        USART_Printf("并行ADC模块初始化成功\r\n");
        ParallelADC_Start();
    } else {
        USART_Printf("并行ADC模块初始化失败\r\n");
    }

    /* 5. 初始化DDS波形生成模块 */
    DDS_Config_t dds_config = {
        .frequency = 1000,              // 1kHz
        .amplitude = 2048,              // 50%幅度
        .offset = 2048,                 // 中心偏移
        .phase = 0,                     // 无相位偏移
        .wave_type = DDS_WAVE_SINE,     // 正弦波
        .enable_interpolation = true,   // 使能线性插值
        .enable_modulation = false,     // 禁用调制
        .sample_rate = 1000000          // 1MSPS
    };

    if (DDS_Init(&dds_config) == 0) {
        USART_Printf("DDS波形生成模块初始化成功\r\n");
        DDS_Start();
    } else {
        USART_Printf("DDS波形生成模块初始化失败\r\n");
    }

    USART_Printf("所有模块初始化完成！\r\n\r\n");
}

/**
  * @brief  模块功能测试演示
  * @param  None
  * @retval None
  */
void Module_Test_Demo(void)
{
    USART_Printf("开始模块功能测试...\r\n");

    /* 测试SysTick精确延时 */
    uint32_t start_time = SysTick_GetTick();
    Delay_ms(100);
    uint32_t elapsed = SysTick_GetTick() - start_time;
    USART_Printf("SysTick延时测试: 期望100ms, 实际%lums\r\n", elapsed);

    /* 测试DDS频率设置 */
    DDS_SetFrequency(2000); // 设置为2kHz
    USART_Printf("DDS频率设置为2kHz\r\n");

    Delay_ms(1000);

    DDS_SetWaveType(DDS_WAVE_SQUARE); // 切换为方波
    USART_Printf("DDS波形切换为方波\r\n");

    /* 测试ADC校准 */
    if (ADC_Calibrate() == 0) {
        USART_Printf("ADC校准完成\r\n");
    }

    USART_Printf("模块功能测试完成！\r\n\r\n");
}

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 系统时钟已经在SystemInit()中配置 */
    /* 这里可以添加额外的时钟配置代码 */
}

/**
  * @brief  毫秒延时函数
  * @param  ms: 延时毫秒数
  * @retval None
  */
void Delay(uint32_t ms)
{
    uint32_t tickstart = 0;
    tickstart = GetTick();
    while((GetTick() - tickstart) < ms)
    {
        // 防止编译器优化掉这个循环
        __NOP();
    }
}

/**
  * @brief  获取系统滴答计数
  * @param  None
  * @retval 当前滴答计数值
  */
uint32_t GetTick(void)
{
    return uwTick;
}

/**
  * @brief  SysTick相关的空减计数函数，供模板代码引用
  */
void TimingDelay_Decrement(void)
{
    /* 已使用uwTick实现延时，此处留空即可满足链接 */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  assert_failed: 发生参数错误时进入此函数
  * @param  file: 源文件名
  * @param  line: 行号
  */
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    (void)file;
    (void)line;
    while (1) {}
}
#endif

/* SysTick中断处理函数中使用的全局变量 */
__IO uint32_t uwTick = 0;
