/**
  ******************************************************************************
  * @file    oled.h
  * <AUTHOR>
  * @version V2.0
  * @date    2024
  * @brief   I2C OLED显示模块 - 硬件适配修正版
  *          基于SSD1306控制器的0.96寸I2C OLED屏幕驱动
  ******************************************************************************
  * @attention
  * 
  * 硬件规格：
  * - 屏幕尺寸：0.96寸 128x64像素
  * - 控制器：SSD1306
  * - 接口类型：4引脚I2C接口（VCC, GND, SCL, SDA）
  * - STM32连接：使用硬件I2C1外设
  *   - SCL -> PB6 (I2C1_SCL)
  *   - SDA -> PB7 (I2C1_SDA)
  * 
  * 功能特性：
  * - 基于STM32F4硬件I2C1外设
  * - 完整的SSD1306初始化序列
  * - 基础显示功能：清屏、字符、字符串、数字
  * - 错误处理和超时机制
  * - 简洁稳定的驱动实现
  *
  ******************************************************************************
  */

#ifndef __OLED_H
#define __OLED_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  OLED显示模式
  */
typedef enum {
    OLED_MODE_NORMAL = 0,        ///< 正常显示
    OLED_MODE_INVERSE = 1        ///< 反色显示
} OLED_DisplayMode_t;

/**
  * @brief  OLED字体大小
  */
typedef enum {
    OLED_FONT_6x8 = 0,          ///< 6x8字体
    OLED_FONT_8x16 = 1          ///< 8x16字体
} OLED_FontSize_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup OLED_Exported_Constants OLED导出常量
  * @{
  */

#define OLED_WIDTH                  128U     ///< OLED屏幕宽度
#define OLED_HEIGHT                 64U      ///< OLED屏幕高度
#define OLED_PAGES                  8U       ///< OLED页数(64/8=8)

#define OLED_I2C_ADDRESS            0x78     ///< OLED I2C地址(7位地址0x3C左移1位)
#define OLED_I2C_TIMEOUT            1000     ///< I2C超时时间(ms)

// SSD1306命令定义
#define OLED_CMD_SET_CONTRAST       0x81     ///< 设置对比度
#define OLED_CMD_DISPLAY_ALL_ON     0xA5     ///< 全屏点亮
#define OLED_CMD_DISPLAY_NORMAL     0xA6     ///< 正常显示
#define OLED_CMD_DISPLAY_INVERSE    0xA7     ///< 反色显示
#define OLED_CMD_DISPLAY_OFF        0xAE     ///< 关闭显示
#define OLED_CMD_DISPLAY_ON         0xAF     ///< 开启显示

#define OLED_CMD_SET_MEMORY_MODE    0x20     ///< 设置内存地址模式
#define OLED_CMD_SET_COLUMN_ADDR    0x21     ///< 设置列地址
#define OLED_CMD_SET_PAGE_ADDR      0x22     ///< 设置页地址

#define OLED_CMD_SET_START_LINE     0x40     ///< 设置显示起始行
#define OLED_CMD_SET_SEGMENT_REMAP  0xA1     ///< 设置段重映射
#define OLED_CMD_SET_MUX_RATIO      0xA8     ///< 设置复用比
#define OLED_CMD_SET_COM_SCAN_DIR   0xC8     ///< 设置COM扫描方向
#define OLED_CMD_SET_DISPLAY_OFFSET 0xD3     ///< 设置显示偏移
#define OLED_CMD_SET_COM_PINS       0xDA     ///< 设置COM引脚配置

#define OLED_CMD_SET_CLOCK_DIV      0xD5     ///< 设置时钟分频
#define OLED_CMD_SET_PRECHARGE      0xD9     ///< 设置预充电周期
#define OLED_CMD_SET_VCOM_DETECT    0xDB     ///< 设置VCOM检测电压
#define OLED_CMD_CHARGE_PUMP        0x8D     ///< 电荷泵设置

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup OLED_Exported_Macros OLED导出宏
  * @{
  */

/**
  * @brief  检查坐标是否有效
  */
#define OLED_IS_VALID_X(x)          ((x) < OLED_WIDTH)
#define OLED_IS_VALID_Y(y)          ((y) < OLED_HEIGHT)
#define OLED_IS_VALID_PAGE(page)    ((page) < OLED_PAGES)

/**
  * @brief  坐标转换
  */
#define OLED_Y_TO_PAGE(y)           ((y) / 8)
#define OLED_Y_TO_BIT(y)            ((y) % 8)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup OLED_Exported_Variables OLED导出变量
  * @{
  */

extern volatile bool g_oled_initialized;        ///< OLED初始化标志

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup OLED_Exported_Functions OLED导出函数
  * @{
  */

/**
  * @brief  OLED初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_Init(void);

/**
  * @brief  OLED反初始化
  * @param  None
  * @retval None
  */
void OLED_DeInit(void);

/**
  * @brief  清屏
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_Clear(void);

/**
  * @brief  刷新显示
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_Refresh(void);

/**
  * @brief  设置显示模式
  * @param  mode: 显示模式
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_SetDisplayMode(OLED_DisplayMode_t mode);

/**
  * @brief  设置对比度
  * @param  contrast: 对比度值(0-255)
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_SetContrast(uint8_t contrast);

/**
  * @brief  显示单个字符
  * @param  x: X坐标(0-127)
  * @param  y: Y坐标(0-63)
  * @param  chr: 要显示的字符
  * @param  font_size: 字体大小
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_ShowChar(uint8_t x, uint8_t y, char chr, OLED_FontSize_t font_size);

/**
  * @brief  显示字符串
  * @param  x: X坐标(0-127)
  * @param  y: Y坐标(0-63)
  * @param  str: 要显示的字符串
  * @param  font_size: 字体大小
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_ShowString(uint8_t x, uint8_t y, const char* str, OLED_FontSize_t font_size);

/**
  * @brief  显示数字
  * @param  x: X坐标(0-127)
  * @param  y: Y坐标(0-63)
  * @param  num: 要显示的数字
  * @param  len: 显示长度
  * @param  font_size: 字体大小
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, OLED_FontSize_t font_size);

/**
  * @brief  显示浮点数
  * @param  x: X坐标(0-127)
  * @param  y: Y坐标(0-63)
  * @param  f_num: 要显示的浮点数
  * @param  int_len: 整数部分长度
  * @param  frac_len: 小数部分长度
  * @param  font_size: 字体大小
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_ShowFloat(uint8_t x, uint8_t y, float f_num, uint8_t int_len, uint8_t frac_len, OLED_FontSize_t font_size);

/**
  * @brief  在指定位置画点
  * @param  x: X坐标(0-127)
  * @param  y: Y坐标(0-63)
  * @param  dot: 点状态(0-清除, 1-点亮)
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_DrawPoint(uint8_t x, uint8_t y, uint8_t dot);

/**
  * @brief  画线
  * @param  x1: 起点X坐标
  * @param  y1: 起点Y坐标
  * @param  x2: 终点X坐标
  * @param  y2: 终点Y坐标
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_DrawLine(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2);

/**
  * @brief  画矩形
  * @param  x: 左上角X坐标
  * @param  y: 左上角Y坐标
  * @param  width: 宽度
  * @param  height: 高度
  * @param  fill: 是否填充(0-不填充, 1-填充)
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_DrawRect(uint8_t x, uint8_t y, uint8_t width, uint8_t height, uint8_t fill);

/**
  * @brief  检查OLED是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool OLED_IsReady(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __OLED_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
