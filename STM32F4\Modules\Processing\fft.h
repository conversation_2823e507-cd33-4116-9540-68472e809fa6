/**
  ******************************************************************************
  * @file    fft.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   FFT计算模块 - 竞赛级实现
  *          基于ARM CMSIS-DSP库的高性能FFT处理
  ******************************************************************************
  * @attention
  * 
  * 本模块特性：
  * 1. 基于ARM CMSIS-DSP库的黄金参考实现
  * 2. 严格按照 init -> rfft -> cmplx_mag 三步执行
  * 3. 支持16-4096点FFT计算
  * 4. 自动峰值检测
  * 5. 基础频谱分析功能
  *
  * 【关键】CMSIS-DSP配置要求：
  * 1. 在Keil项目的C/C++选项中，Include Paths添加：
  *    ..\..\Libraries\CMSIS\DSP\Include
  * 2. 在C/C++选项的Define中添加：
  *    ARM_MATH_CM4,__FPU_PRESENT=1
  * 3. 在Linker选项中添加库文件：
  *    arm_cortexM4lf_math.lib
  * 4. 确保项目使用硬件FPU：
  *    Target选项卡中，Floating Point Hardware选择Use FPU
  *
  ******************************************************************************
  */

#ifndef __FFT_H
#define __FFT_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

// CMSIS-DSP库头文件
#ifdef ARM_MATH_CM4
#include "arm_math.h"
#include "arm_const_structs.h"
#else
#warning "ARM_MATH_CM4 not defined. Please add ARM_MATH_CM4 to preprocessor definitions."
#endif

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  FFT长度枚举
  */
typedef enum {
    FFT_SIZE_64 = 64,            ///< 64点FFT
    FFT_SIZE_128 = 128,          ///< 128点FFT
    FFT_SIZE_256 = 256,          ///< 256点FFT
    FFT_SIZE_512 = 512,          ///< 512点FFT
    FFT_SIZE_1024 = 1024,        ///< 1024点FFT
    FFT_SIZE_2048 = 2048,        ///< 2048点FFT
    FFT_SIZE_4096 = 4096         ///< 4096点FFT
} FFT_Size_t;

/**
  * @brief  窗函数类型枚举
  */
typedef enum {
    FFT_WINDOW_NONE = 0,         ///< 矩形窗(无窗)
    FFT_WINDOW_HANNING,          ///< 汉宁窗
    FFT_WINDOW_HAMMING,          ///< 海明窗
    FFT_WINDOW_BLACKMAN,         ///< 布莱克曼窗
    FFT_WINDOW_KAISER,           ///< 凯泽窗
    FFT_WINDOW_FLATTOP,          ///< 平顶窗
    FFT_WINDOW_COUNT             ///< 窗函数总数
} FFT_WindowType_t;

/**
  * @brief  FFT配置结构体
  */
typedef struct {
    FFT_Size_t fft_size;         ///< FFT长度
    FFT_WindowType_t window_type; ///< 窗函数类型
    uint32_t sample_rate;        ///< 采样率(Hz)
    bool enable_overlap;         ///< 使能重叠处理
    uint8_t overlap_ratio;       ///< 重叠比例(25%, 50%, 75%)
    bool enable_zero_padding;    ///< 使能零填充
    bool enable_magnitude_db;    ///< 使能dB输出
    float kaiser_beta;           ///< Kaiser窗参数
} FFT_Config_t;

/**
  * @brief  FFT结果结构体
  */
typedef struct {
    float32_t* magnitude;        ///< 幅度谱
    float32_t* phase;            ///< 相位谱
    float32_t* power_spectrum;   ///< 功率谱
    uint32_t bin_count;          ///< 频率bin数量
    float32_t frequency_resolution; ///< 频率分辨率(Hz)
    float32_t peak_frequency;    ///< 峰值频率
    float32_t peak_magnitude;    ///< 峰值幅度
    uint32_t peak_bin;           ///< 峰值bin索引
} FFT_Result_t;

/**
  * @brief  FFT统计信息结构体
  */
typedef struct {
    uint32_t fft_count;          ///< FFT计算次数
    uint32_t peak_detect_count;  ///< 峰值检测次数
    float32_t avg_computation_time; ///< 平均计算时间(ms)
    float32_t max_computation_time; ///< 最大计算时间(ms)
    float32_t snr_db;            ///< 信噪比(dB)
    float32_t thd_db;            ///< 总谐波失真(dB)
    uint32_t overflow_count;     ///< 溢出次数
} FFT_Stats_t;

/**
  * @brief  FFT句柄结构体
  */
typedef struct {
    FFT_Config_t config;         ///< 配置参数
    FFT_Result_t result;         ///< 计算结果
    FFT_Stats_t stats;           ///< 统计信息
    
    // CMSIS-DSP实例
#ifdef ARM_MATH_CM4
    arm_rfft_fast_instance_f32 rfft_instance; ///< 实数FFT实例
    arm_cfft_radix4_instance_f32 cfft_instance; ///< 复数FFT实例
#endif
    
    // 内部缓冲区
    float32_t* input_buffer;     ///< 输入缓冲区
    float32_t* output_buffer;    ///< 输出缓冲区
    float32_t* window_buffer;    ///< 窗函数缓冲区
    float32_t* overlap_buffer;   ///< 重叠缓冲区
    
    // 状态标志
    volatile bool is_initialized; ///< 初始化标志
    volatile bool is_computing;  ///< 计算中标志
    volatile bool result_ready;  ///< 结果就绪标志
} FFT_Handle_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup FFT_Exported_Constants FFT导出常量
  * @{
  */

#define FFT_MAX_SIZE                4096U    ///< 最大FFT长度
#define FFT_MIN_SIZE                64U      ///< 最小FFT长度

#define FFT_OVERLAP_25_PERCENT      25U      ///< 25%重叠
#define FFT_OVERLAP_50_PERCENT      50U      ///< 50%重叠
#define FFT_OVERLAP_75_PERCENT      75U      ///< 75%重叠

#define FFT_DB_MIN_VALUE            -120.0f  ///< 最小dB值
#define FFT_MAGNITUDE_THRESHOLD     1e-10f   ///< 幅度阈值

// Kaiser窗参数
#define FFT_KAISER_BETA_DEFAULT     8.6f     ///< 默认Kaiser窗参数
#define FFT_KAISER_BETA_MIN         0.0f     ///< 最小Kaiser窗参数
#define FFT_KAISER_BETA_MAX         20.0f    ///< 最大Kaiser窗参数

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup FFT_Exported_Macros FFT导出宏
  * @{
  */

/**
  * @brief  线性值转换为dB
  */
#define FFT_LINEAR_TO_DB(x)         (20.0f * log10f((x) + 1e-10f))

/**
  * @brief  dB值转换为线性值
  */
#define FFT_DB_TO_LINEAR(x)         (powf(10.0f, (x) / 20.0f))

/**
  * @brief  频率bin转换为实际频率
  */
#define FFT_BIN_TO_FREQ(bin, sample_rate, fft_size) \
    ((float32_t)(bin) * (sample_rate) / (fft_size))

/**
  * @brief  实际频率转换为频率bin
  */
#define FFT_FREQ_TO_BIN(freq, sample_rate, fft_size) \
    ((uint32_t)((freq) * (fft_size) / (sample_rate)))

/**
  * @brief  检查FFT是否初始化
  */
#define FFT_IS_INITIALIZED(handle)  ((handle)->is_initialized)

/**
  * @brief  检查FFT是否在计算
  */
#define FFT_IS_COMPUTING(handle)    ((handle)->is_computing)

/**
  * @brief  检查FFT结果是否就绪
  */
#define FFT_IS_RESULT_READY(handle) ((handle)->result_ready)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup FFT_Exported_Variables FFT导出变量
  * @{
  */

extern FFT_Handle_t g_fft_handle;               ///< FFT句柄
extern volatile bool g_fft_computation_complete; ///< FFT计算完成标志

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup FFT_Exported_Functions FFT导出函数
  * @{
  */

/**
  * @brief  FFT模块初始化
  * @param  config: 配置参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t FFT_Init(FFT_Config_t* config);

/**
  * @brief  FFT模块反初始化
  * @param  None
  * @retval None
  */
void FFT_DeInit(void);

/**
  * @brief  计算实数FFT
  * @param  input: 输入数据指针
  * @param  length: 数据长度
  * @retval 0: 成功, -1: 失败
  */
int8_t FFT_ComputeReal(const float32_t* input, uint32_t length);

/**
  * @brief  计算复数FFT
  * @param  input_real: 实部输入数据
  * @param  input_imag: 虚部输入数据
  * @param  length: 数据长度
  * @retval 0: 成功, -1: 失败
  */
int8_t FFT_ComputeComplex(const float32_t* input_real, const float32_t* input_imag, uint32_t length);

/**
  * @brief  应用窗函数
  * @param  data: 数据指针
  * @param  length: 数据长度
  * @param  window_type: 窗函数类型
  * @retval 0: 成功, -1: 失败
  */
int8_t FFT_ApplyWindow(float32_t* data, uint32_t length, FFT_WindowType_t window_type);

/**
  * @brief  计算功率谱密度
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t FFT_ComputePowerSpectrum(void);

/**
  * @brief  峰值检测
  * @param  threshold: 检测阈值
  * @param  min_distance: 最小间距
  * @retval 检测到的峰值数量
  */
uint32_t FFT_PeakDetection(float32_t threshold, uint32_t min_distance);

/**
  * @brief  频率估计
  * @param  None
  * @retval 估计的主频率(Hz)
  */
float32_t FFT_EstimateFrequency(void);

/**
  * @brief  计算信噪比
  * @param  signal_bin: 信号bin索引
  * @param  noise_bins: 噪声bin数量
  * @retval 信噪比(dB)
  */
float32_t FFT_CalculateSNR(uint32_t signal_bin, uint32_t noise_bins);

/**
  * @brief  计算总谐波失真
  * @param  fundamental_bin: 基波bin索引
  * @param  harmonic_count: 谐波数量
  * @retval THD(dB)
  */
float32_t FFT_CalculateTHD(uint32_t fundamental_bin, uint32_t harmonic_count);

/**
  * @brief  获取FFT结果
  * @param  result: 结果结构体指针
  * @retval 0: 成功, -1: 失败
  */
int8_t FFT_GetResult(FFT_Result_t* result);

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void FFT_GetStats(FFT_Stats_t* stats);

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void FFT_ResetStats(void);

/**
  * @brief  设置窗函数参数
  * @param  window_type: 窗函数类型
  * @param  parameter: 窗函数参数(如Kaiser窗的beta值)
  * @retval 0: 成功, -1: 失败
  */
int8_t FFT_SetWindowParameter(FFT_WindowType_t window_type, float32_t parameter);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __FFT_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
