/**
  ******************************************************************************
  * @file    adc_dma.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   ADC+DMA多通道采集模块实现
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "adc_dma.h"
#include "systick.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/** @defgroup ADC_DMA_Private_Defines ADC_DMA私有定义
  * @{
  */

#define ADC_GPIO_PORT               GPIOA
#define ADC_GPIO_CLK                RCC_AHB1Periph_GPIOA
#define ADC_CLK                     RCC_APB2Periph_ADC1
#define ADC_DMA_CLK                 RCC_AHB1Periph_DMA2

#define ADC_PIN_CH1                 GPIO_Pin_1   ///< PA1 - ADC1_IN1
#define ADC_PIN_CH2                 GPIO_Pin_2   ///< PA2 - ADC1_IN2
#define ADC_PIN_CH3                 GPIO_Pin_3   ///< PA3 - ADC1_IN3

#define ADC_DMA_STREAM              DMA2_Stream0
#define ADC_DMA_CHANNEL             DMA_Channel_0

// 数字滤波器参数
#define FILTER_ALPHA                0.1f         ///< 低通滤波器系数
#define OVERSAMPLE_SHIFT_12BIT      4            ///< 12位过采样位移
#define OVERSAMPLE_SHIFT_10BIT      3            ///< 10位过采样位移

/**
  * @}
  */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/** @defgroup ADC_DMA_Private_Variables ADC_DMA私有变量
  * @{
  */

ADC_Handle_t g_adc1_handle;                     ///< ADC1句柄
volatile bool g_adc_conversion_complete = false; ///< 转换完成标志
volatile bool g_adc_dma_half_complete = false;  ///< DMA半传输完成标志
volatile bool g_adc_dma_full_complete = false;  ///< DMA全传输完成标志

// 双缓冲区
static uint16_t s_adc_ping_buffer[ADC_BUFFER_SIZE];
static uint16_t s_adc_pong_buffer[ADC_BUFFER_SIZE];

// 滤波缓冲区
static float s_filter_buffer[ADC_CHANNEL_COUNT];
static uint32_t s_oversample_buffer[ADC_CHANNEL_COUNT];

/**
  * @}
  */

/* Private function prototypes -----------------------------------------------*/
static void ADC_GPIO_Config(void);
static void ADC_Config(ADC_Config_t* config);
static void ADC_DMA_Config(void);
static void ADC_NVIC_Config(void);
static void ADC_Timer_Config(uint32_t sample_rate);
static uint16_t ADC_ApplyCalibration(uint16_t raw_value, ADC_Channel_t channel);
static void ADC_UpdateStats(uint16_t* buffer, uint32_t length);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  ADC GPIO配置
  * @param  None
  * @retval None
  */
static void ADC_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIO时钟
    RCC_AHB1PeriphClockCmd(ADC_GPIO_CLK, ENABLE);
    
    // 配置ADC引脚为模拟输入
    GPIO_InitStructure.GPIO_Pin = ADC_PIN_CH1 | ADC_PIN_CH2 | ADC_PIN_CH3;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(ADC_GPIO_PORT, &GPIO_InitStructure);
}

/**
  * @brief  ADC配置
  * @param  config: 配置参数
  * @retval None
  */
static void ADC_Config(ADC_Config_t* config)
{
    ADC_InitTypeDef ADC_InitStructure;
    ADC_CommonInitTypeDef ADC_CommonInitStructure;
    
    // 使能ADC时钟
    RCC_APB2PeriphClockCmd(ADC_CLK, ENABLE);
    
    // 配置ADC公共参数
    ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;
    ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4; // 42MHz
    ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_Disabled;
    ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;
    ADC_CommonInit(&ADC_CommonInitStructure);
    
    // 配置ADC参数
    ADC_InitStructure.ADC_Resolution = config->resolution;
    ADC_InitStructure.ADC_ScanConvMode = ENABLE;  // 扫描模式
    ADC_InitStructure.ADC_ContinuousConvMode = (config->mode == ADC_MODE_CONTINUOUS) ? ENABLE : DISABLE;
    ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_None;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T1_CC1;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfConversion = ADC_CHANNEL_COUNT;
    ADC_Init(ADC1, &ADC_InitStructure);
    
    // 配置ADC通道
    ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 1, ADC_SampleTime_15Cycles);
    ADC_RegularChannelConfig(ADC1, ADC_Channel_2, 2, ADC_SampleTime_15Cycles);
    ADC_RegularChannelConfig(ADC1, ADC_Channel_3, 3, ADC_SampleTime_15Cycles);
    
    // 使能DMA请求
    ADC_DMARequestAfterLastTransferCmd(ADC1, ENABLE);
    ADC_DMACmd(ADC1, ENABLE);
    
    // 使能ADC
    ADC_Cmd(ADC1, ENABLE);
}

/**
  * @brief  ADC DMA配置
  * @param  None
  * @retval None
  */
static void ADC_DMA_Config(void)
{
    DMA_InitTypeDef DMA_InitStructure;
    
    // 使能DMA时钟
    RCC_AHB1PeriphClockCmd(ADC_DMA_CLK, ENABLE);
    
    // 配置DMA
    DMA_DeInit(ADC_DMA_STREAM);
    DMA_InitStructure.DMA_Channel = ADC_DMA_CHANNEL;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&ADC1->DR;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)s_adc_ping_buffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;
    DMA_InitStructure.DMA_BufferSize = ADC_BUFFER_SIZE;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;  // 循环模式
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
    DMA_Init(ADC_DMA_STREAM, &DMA_InitStructure);
    
    // 配置双缓冲模式
    DMA_DoubleBufferModeConfig(ADC_DMA_STREAM, (uint32_t)s_adc_pong_buffer, DMA_Memory_0);
    DMA_DoubleBufferModeCmd(ADC_DMA_STREAM, ENABLE);
    
    // 使能DMA中断
    DMA_ITConfig(ADC_DMA_STREAM, DMA_IT_TC, ENABLE);   // 传输完成中断
    DMA_ITConfig(ADC_DMA_STREAM, DMA_IT_HT, ENABLE);   // 半传输中断
    DMA_ITConfig(ADC_DMA_STREAM, DMA_IT_TE, ENABLE);   // 传输错误中断
}

/**
  * @brief  ADC NVIC配置
  * @param  None
  * @retval None
  */
static void ADC_NVIC_Config(void)
{
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 配置ADC中断
    NVIC_InitStructure.NVIC_IRQChannel = ADC_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    // 配置DMA中断
    NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream0_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;  // 最高优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
  * @brief  定时器配置(用于触发ADC)
  * @param  sample_rate: 采样率
  * @retval None
  */
static void ADC_Timer_Config(uint32_t sample_rate)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    
    // 使能TIM2时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
    
    // 计算定时器参数
    uint32_t timer_freq = 84000000; // APB1时钟84MHz
    uint32_t period = timer_freq / sample_rate;
    
    // 配置定时器
    TIM_TimeBaseStructure.TIM_Period = period - 1;
    TIM_TimeBaseStructure.TIM_Prescaler = 0;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);
    
    // 配置TIM2触发输出
    TIM_SelectOutputTrigger(TIM2, TIM_TRGOSource_Update);
    
    // 使能定时器
    TIM_Cmd(TIM2, ENABLE);
}

/**
  * @brief  应用校准参数
  * @param  raw_value: 原始ADC值
  * @param  channel: 通道号
  * @retval 校准后的ADC值
  */
static uint16_t ADC_ApplyCalibration(uint16_t raw_value, ADC_Channel_t channel)
{
    if (!g_adc1_handle.calibration.calibrated || channel >= ADC_CHANNEL_COUNT) {
        return raw_value;
    }
    
    // 应用偏移和增益校准
    float calibrated = (float)raw_value - g_adc1_handle.calibration.offset[channel];
    calibrated *= g_adc1_handle.calibration.gain[channel];
    
    // 限制范围
    if (calibrated < 0) calibrated = 0;
    if (calibrated > 4095) calibrated = 4095;
    
    return (uint16_t)calibrated;
}

/**
  * @brief  更新统计信息
  * @param  buffer: 数据缓冲区
  * @param  length: 数据长度
  * @retval None
  */
static void ADC_UpdateStats(uint16_t* buffer, uint32_t length)
{
    for (uint32_t i = 0; i < length; i += ADC_CHANNEL_COUNT) {
        for (uint8_t ch = 0; ch < ADC_CHANNEL_COUNT; ch++) {
            uint16_t value = buffer[i + ch];
            
            // 更新最大最小值
            if (value > g_adc1_handle.stats.max_values[ch]) {
                g_adc1_handle.stats.max_values[ch] = value;
            }
            if (value < g_adc1_handle.stats.min_values[ch]) {
                g_adc1_handle.stats.min_values[ch] = value;
            }
            
            // 更新平均值(移动平均)
            g_adc1_handle.stats.avg_values[ch] = 
                (g_adc1_handle.stats.avg_values[ch] * 15 + value) / 16;
        }
    }
    
    g_adc1_handle.stats.total_samples += length;
}

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  ADC1+DMA初始化
  * @param  config: 配置参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t ADC1_DMA_Init(ADC_Config_t* config)
{
    if (config == NULL) {
        return -1;
    }

    // 初始化句柄
    g_adc1_handle.instance = ADC1;
    g_adc1_handle.dma_stream = ADC_DMA_STREAM;
    g_adc1_handle.config = *config;
    g_adc1_handle.is_running = false;

    // 初始化缓冲区
    g_adc1_handle.buffer.ping_buffer = s_adc_ping_buffer;
    g_adc1_handle.buffer.pong_buffer = s_adc_pong_buffer;
    g_adc1_handle.buffer.buffer_size = ADC_BUFFER_SIZE;
    g_adc1_handle.buffer.active_buffer = 0;
    g_adc1_handle.buffer.data_ready = false;
    g_adc1_handle.buffer.sample_count = 0;

    // 初始化校准参数
    for (uint8_t i = 0; i < ADC_CHANNEL_COUNT; i++) {
        g_adc1_handle.calibration.gain[i] = 1.0f;
        g_adc1_handle.calibration.offset[i] = 0;
    }
    g_adc1_handle.calibration.reference_voltage = ADC_VREF_VOLTAGE_MV;
    g_adc1_handle.calibration.calibrated = false;

    // 配置硬件
    ADC_GPIO_Config();
    ADC_DMA_Config();
    ADC_NVIC_Config();
    ADC_Config(config);

    // 配置采样定时器
    if (config->mode == ADC_MODE_TRIGGERED) {
        ADC_Timer_Config(config->sample_rate);
    }

    // 重置统计信息
    ADC_ResetStats();

    return 0;
}

/**
  * @brief  开始ADC采集
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t ADC_Start_Acquisition(void)
{
    if (g_adc1_handle.is_running) {
        return -1; // 已经在运行
    }

    // 清除标志
    g_adc_conversion_complete = false;
    g_adc_dma_half_complete = false;
    g_adc_dma_full_complete = false;
    g_adc1_handle.buffer.data_ready = false;

    // 启动DMA
    DMA_Cmd(ADC_DMA_STREAM, ENABLE);

    // 启动ADC转换
    ADC_SoftwareStartConv(ADC1);

    g_adc1_handle.is_running = true;

    return 0;
}

/**
  * @brief  停止ADC采集
  * @param  None
  * @retval None
  */
void ADC_Stop_Acquisition(void)
{
    // 停止ADC转换
    ADC_SoftwareStartConv(ADC1);

    // 停止DMA
    DMA_Cmd(ADC_DMA_STREAM, DISABLE);

    g_adc1_handle.is_running = false;
}

/**
  * @brief  获取单通道ADC值
  * @param  channel: 通道号
  * @retval ADC值
  */
uint16_t ADC_GetChannelValue(ADC_Channel_t channel)
{
    if (channel >= ADC_CHANNEL_COUNT || !g_adc1_handle.buffer.data_ready) {
        return 0;
    }

    // 获取当前非活动缓冲区的最新数据
    uint16_t* buffer = ADC_GET_INACTIVE_BUFFER(&g_adc1_handle);
    uint16_t raw_value = buffer[channel];

    // 应用校准
    return ADC_ApplyCalibration(raw_value, channel);
}

/**
  * @brief  获取单通道电压值(mV)
  * @param  channel: 通道号
  * @retval 电压值(mV)
  */
uint32_t ADC_GetChannelVoltage_mV(ADC_Channel_t channel)
{
    uint16_t adc_value = ADC_GetChannelValue(channel);
    return ADC_TO_VOLTAGE_MV(adc_value, ADC_MAX_VALUE_12BIT);
}

/**
  * @brief  获取多通道数据
  * @param  data: 数据缓冲区
  * @param  length: 缓冲区长度
  * @retval 实际获取的数据长度
  */
uint32_t ADC_GetMultiChannelData(uint16_t* data, uint32_t length)
{
    if (data == NULL || length == 0 || !g_adc1_handle.buffer.data_ready) {
        return 0;
    }

    // 获取当前非活动缓冲区
    uint16_t* buffer = ADC_GET_INACTIVE_BUFFER(&g_adc1_handle);
    uint32_t copy_length = (length < ADC_BUFFER_SIZE) ? length : ADC_BUFFER_SIZE;

    // 复制数据并应用校准
    for (uint32_t i = 0; i < copy_length; i += ADC_CHANNEL_COUNT) {
        for (uint8_t ch = 0; ch < ADC_CHANNEL_COUNT && (i + ch) < copy_length; ch++) {
            data[i + ch] = ADC_ApplyCalibration(buffer[i + ch], (ADC_Channel_t)ch);
        }
    }

    // 清除数据就绪标志
    g_adc1_handle.buffer.data_ready = false;

    return copy_length;
}

/**
  * @brief  处理ADC数据
  * @param  None
  * @retval None
  */
void ADC_ProcessData(void)
{
    if (!g_adc1_handle.buffer.data_ready) {
        return;
    }

    // 获取当前非活动缓冲区
    uint16_t* buffer = ADC_GET_INACTIVE_BUFFER(&g_adc1_handle);

    // 更新统计信息
    ADC_UpdateStats(buffer, ADC_BUFFER_SIZE);

    // 应用数字滤波
    if (g_adc1_handle.config.enable_filter) {
        for (uint32_t i = 0; i < ADC_BUFFER_SIZE; i += ADC_CHANNEL_COUNT) {
            for (uint8_t ch = 0; ch < ADC_CHANNEL_COUNT; ch++) {
                float current_value = (float)buffer[i + ch];
                s_filter_buffer[ch] = s_filter_buffer[ch] * (1.0f - FILTER_ALPHA) +
                                     current_value * FILTER_ALPHA;
                buffer[i + ch] = (uint16_t)s_filter_buffer[ch];
            }
        }
    }
}

/**
  * @brief  ADC校准
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t ADC_Calibrate(void)
{
    // 简化的校准算法
    // 在实际应用中，应该使用已知的参考电压进行校准

    if (!g_adc1_handle.is_running) {
        return -1;
    }

    // 采集校准数据
    uint32_t sum[ADC_CHANNEL_COUNT] = {0};
    uint32_t samples = 1000;

    for (uint32_t i = 0; i < samples; i++) {
        // 等待新数据
        while (!g_adc1_handle.buffer.data_ready) {
            Delay_ms(1);  // 修复：使用Delay_ms替代Delay_us
        }

        uint16_t* buffer = ADC_GET_INACTIVE_BUFFER(&g_adc1_handle);
        for (uint8_t ch = 0; ch < ADC_CHANNEL_COUNT; ch++) {
            sum[ch] += buffer[ch];
        }

        g_adc1_handle.buffer.data_ready = false;
    }

    // 计算平均值作为偏移校准
    for (uint8_t ch = 0; ch < ADC_CHANNEL_COUNT; ch++) {
        uint16_t avg = sum[ch] / samples;
        // 假设输入为0V时的理想值为0
        g_adc1_handle.calibration.offset[ch] = avg;
        g_adc1_handle.calibration.gain[ch] = 1.0f; // 简化处理
    }

    g_adc1_handle.calibration.calibrated = true;
    g_adc1_handle.stats.calibration_count++;

    return 0;
}

/**
  * @brief  设置采样率
  * @param  sample_rate: 采样率(Hz)
  * @retval 0: 成功, -1: 失败
  */
int8_t ADC_SetSampleRate(uint32_t sample_rate)
{
    if (sample_rate < ADC_MIN_SAMPLE_RATE || sample_rate > ADC_MAX_SAMPLE_RATE) {
        return -1;
    }

    g_adc1_handle.config.sample_rate = sample_rate;

    // 重新配置定时器
    if (g_adc1_handle.config.mode == ADC_MODE_TRIGGERED) {
        ADC_Timer_Config(sample_rate);
    }

    return 0;
}

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void ADC_GetStats(ADC_Stats_t* stats)
{
    if (stats == NULL) {
        return;
    }

    *stats = g_adc1_handle.stats;

    // 计算实际采样率
    static uint32_t last_samples = 0;
    static uint32_t last_time = 0;
    uint32_t current_time = SysTick_GetTick();

    if (current_time > last_time + 1000) { // 每秒更新一次
        uint32_t samples_diff = stats->total_samples - last_samples;
        uint32_t time_diff = current_time - last_time;
        stats->actual_sample_rate = (float)samples_diff * 1000.0f / time_diff;

        last_samples = stats->total_samples;
        last_time = current_time;
    }
}

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void ADC_ResetStats(void)
{
    memset(&g_adc1_handle.stats, 0, sizeof(ADC_Stats_t));

    // 初始化最小值
    for (uint8_t i = 0; i < ADC_CHANNEL_COUNT; i++) {
        g_adc1_handle.stats.min_values[i] = 0xFFFF;
    }
}

/**
  * @brief  ADC中断处理函数
  * @param  None
  * @retval None
  */
void ADC_IRQHandler(void)
{
    // 处理ADC转换完成中断
    if (ADC_GetITStatus(ADC1, ADC_IT_EOC) != RESET) {
        ADC_ClearITPendingBit(ADC1, ADC_IT_EOC);
        g_adc_conversion_complete = true;
    }

    // 处理ADC错误中断
    if (ADC_GetITStatus(ADC1, ADC_IT_OVR) != RESET) {
        ADC_ClearITPendingBit(ADC1, ADC_IT_OVR);
        // 处理溢出错误
        g_adc1_handle.stats.buffer_overruns++;
    }
}

/**
  * @brief  DMA2_Stream0中断处理函数
  * @param  None
  * @retval None
  */
void DMA2_Stream0_IRQHandler(void)
{
    // 检查半传输完成中断
    if (DMA_GetITStatus(ADC_DMA_STREAM, DMA_IT_HTIF0) != RESET) {
        DMA_ClearITPendingBit(ADC_DMA_STREAM, DMA_IT_HTIF0);
        g_adc_dma_half_complete = true;
    }

    // 检查传输完成中断
    if (DMA_GetITStatus(ADC_DMA_STREAM, DMA_IT_TCIF0) != RESET) {
        DMA_ClearITPendingBit(ADC_DMA_STREAM, DMA_IT_TCIF0);

        // 切换缓冲区
        g_adc1_handle.buffer.active_buffer = 1 - g_adc1_handle.buffer.active_buffer;
        g_adc1_handle.buffer.data_ready = true;
        g_adc1_handle.buffer.sample_count++;

        // 更新统计信息
        g_adc1_handle.stats.dma_transfers++;
        g_adc_dma_full_complete = true;
    }

    // 检查传输错误中断
    if (DMA_GetITStatus(ADC_DMA_STREAM, DMA_IT_TEIF0) != RESET) {
        DMA_ClearITPendingBit(ADC_DMA_STREAM, DMA_IT_TEIF0);
        g_adc1_handle.stats.buffer_overruns++;
    }
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
