/**
  ******************************************************************************
  * @file    key.h
  * <AUTHOR>
  * @version V2.0
  * @date    2024
  * @brief   独立按键扫描模块 - 简化稳定版
  *          基础按键扫描，软件消抖，简洁可靠
  ******************************************************************************
  * @attention
  * 
  * 硬件连接：
  * - KEY0: PE4 (低电平有效)
  * - KEY1: PE3 (低电平有效)
  * 
  * 功能特性：
  * - 基础按键扫描
  * - 软件消抖
  * - 非阻塞式检测
  * - 简洁的API接口
  *
  ******************************************************************************
  */

#ifndef __KEY_H
#define __KEY_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  按键编号枚举
  */
typedef enum {
    KEY_NONE = 0,               ///< 无按键按下
    KEY_0 = 1,                  ///< KEY0按下
    KEY_1 = 2,                  ///< KEY1按下
} Key_Number_t;

/**
  * @brief  按键状态枚举
  */
typedef enum {
    KEY_STATE_RELEASED = 0,     ///< 按键释放
    KEY_STATE_PRESSED = 1,      ///< 按键按下
    KEY_STATE_DEBOUNCE = 2      ///< 消抖中
} Key_State_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup KEY_Exported_Constants 按键导出常量
  * @{
  */

#define KEY_DEBOUNCE_TIME_MS        20U      ///< 消抖时间(ms)
#define KEY_SCAN_INTERVAL_MS        10U      ///< 扫描间隔(ms)

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup KEY_Exported_Macros 按键导出宏
  * @{
  */

/**
  * @brief  读取按键状态
  */
#define KEY0_READ()                 GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_4)
#define KEY1_READ()                 GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_3)

/**
  * @brief  检查按键是否按下(低电平有效)
  */
#define KEY0_PRESSED()              (KEY0_READ() == RESET)
#define KEY1_PRESSED()              (KEY1_READ() == RESET)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup KEY_Exported_Variables 按键导出变量
  * @{
  */

extern volatile bool g_key_initialized;         ///< 按键初始化标志

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup KEY_Exported_Functions 按键导出函数
  * @{
  */

/**
  * @brief  按键初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t Key_Init(void);

/**
  * @brief  按键反初始化
  * @param  None
  * @retval None
  */
void Key_DeInit(void);

/**
  * @brief  按键扫描
  * @param  None
  * @retval 按键编号(KEY_NONE/KEY_0/KEY_1)
  */
Key_Number_t Key_Scan(void);

/**
  * @brief  获取按键状态
  * @param  key_num: 按键编号
  * @retval 按键状态
  */
Key_State_t Key_GetState(Key_Number_t key_num);

/**
  * @brief  检查按键是否按下
  * @param  key_num: 按键编号
  * @retval true: 按下, false: 未按下
  */
bool Key_IsPressed(Key_Number_t key_num);

/**
  * @brief  等待按键按下
  * @param  key_num: 按键编号
  * @param  timeout_ms: 超时时间(ms), 0表示无限等待
  * @retval true: 按键按下, false: 超时
  */
bool Key_WaitPress(Key_Number_t key_num, uint32_t timeout_ms);

/**
  * @brief  等待按键释放
  * @param  key_num: 按键编号
  * @param  timeout_ms: 超时时间(ms), 0表示无限等待
  * @retval true: 按键释放, false: 超时
  */
bool Key_WaitRelease(Key_Number_t key_num, uint32_t timeout_ms);

/**
  * @brief  检查按键是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool Key_IsReady(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __KEY_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
