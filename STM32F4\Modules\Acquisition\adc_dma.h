/**
  ******************************************************************************
  * @file    adc_dma.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   ADC+DMA多通道采集模块 - 竞赛级实现
  *          支持双缓冲、过采样、数字滤波、自动校准等高级功能
  ******************************************************************************
  * @attention
  * 
  * 本模块特性：
  * 1. ADC1多通道连续扫描模式
  * 2. DMA2基础数据传输
  * 3. 稳定的多通道采集
  * 4. 基础数据缓冲管理
  * 5. 简洁的驱动接口
  * 
  * 硬件连接：
  * - ADC1_IN1: PA1 (通道1)
  * - ADC1_IN2: PA2 (通道2)  
  * - ADC1_IN3: PA3 (通道3)
  * - DMA2_Stream0, Channel 0
  * - 参考电压: 3.3V
  *
  ******************************************************************************
  */

#ifndef __ADC_DMA_H
#define __ADC_DMA_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  ADC通道枚举
  */
typedef enum {
    ADC_CHANNEL_1 = 0,           ///< PA1 - ADC1_IN1
    ADC_CHANNEL_2 = 1,           ///< PA2 - ADC1_IN2
    ADC_CHANNEL_3 = 2,           ///< PA3 - ADC1_IN3
    ADC_CHANNEL_COUNT = 3        ///< 总通道数
} ADC_Channel_t;

/**
  * @brief  ADC采样模式
  */
typedef enum {
    ADC_MODE_SINGLE = 0,         ///< 单次采样
    ADC_MODE_CONTINUOUS,         ///< 连续采样
    ADC_MODE_TRIGGERED           ///< 触发采样
} ADC_Mode_t;

/**
  * @brief  ADC配置结构体
  */
typedef struct {
    uint32_t sample_rate;        ///< 采样率(Hz) - 保守设置
    uint16_t resolution;         ///< 分辨率(12位固定)
    ADC_Mode_t mode;             ///< 采样模式
} ADC_Config_t;

/**
  * @brief  ADC校准参数结构体
  */
typedef struct {
    float gain[ADC_CHANNEL_COUNT];      ///< 增益校准
    int16_t offset[ADC_CHANNEL_COUNT];  ///< 偏移校准
    uint16_t reference_voltage;         ///< 参考电压(mV)
    bool calibrated;                    ///< 校准完成标志
} ADC_Calibration_t;

/**
  * @brief  ADC统计信息结构体
  */
typedef struct {
    uint32_t total_samples;             ///< 总采样数
    uint32_t dma_transfers;             ///< DMA传输次数
    uint32_t buffer_overruns;           ///< 缓冲区溢出次数
    uint32_t calibration_count;         ///< 校准次数
    float actual_sample_rate;           ///< 实际采样率
    uint16_t max_values[ADC_CHANNEL_COUNT];  ///< 各通道最大值
    uint16_t min_values[ADC_CHANNEL_COUNT];  ///< 各通道最小值
    uint32_t avg_values[ADC_CHANNEL_COUNT];  ///< 各通道平均值
} ADC_Stats_t;

/**
  * @brief  ADC数据缓冲区结构体
  */
typedef struct {
    uint16_t* ping_buffer;              ///< Ping缓冲区
    uint16_t* pong_buffer;              ///< Pong缓冲区
    uint32_t buffer_size;               ///< 缓冲区大小
    volatile uint8_t active_buffer;     ///< 当前活动缓冲区(0=ping, 1=pong)
    volatile bool data_ready;           ///< 数据就绪标志
    volatile uint32_t sample_count;     ///< 采样计数
} ADC_Buffer_t;

/**
  * @brief  ADC句柄结构体
  */
typedef struct {
    ADC_TypeDef* instance;              ///< ADC实例
    DMA_Stream_TypeDef* dma_stream;     ///< DMA流
    ADC_Config_t config;                ///< 配置参数
    ADC_Calibration_t calibration;     ///< 校准参数
    ADC_Buffer_t buffer;                ///< 数据缓冲区
    ADC_Stats_t stats;                  ///< 统计信息
    volatile bool is_running;           ///< 运行状态
} ADC_Handle_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup ADC_DMA_Exported_Constants ADC_DMA导出常量
  * @{
  */

#define ADC_BUFFER_SIZE                 4096U    ///< 单个缓冲区大小
#define ADC_TOTAL_BUFFER_SIZE           (ADC_BUFFER_SIZE * 2)  ///< 总缓冲区大小

#define ADC_MAX_SAMPLE_RATE             500000U  ///< 保守最大采样率(500KSPS)
#define ADC_MIN_SAMPLE_RATE             1000U    ///< 最小采样率(1KSPS)
#define ADC_DEFAULT_SAMPLE_RATE         100000U  ///< 默认采样率(100KSPS)

#define ADC_RESOLUTION_12BIT            ADC_Resolution_12b
#define ADC_RESOLUTION_10BIT            ADC_Resolution_10b
#define ADC_RESOLUTION_8BIT             ADC_Resolution_8b
#define ADC_RESOLUTION_6BIT             ADC_Resolution_6b

#define ADC_VREF_VOLTAGE_MV             3300U    ///< 参考电压(mV)
#define ADC_MAX_VALUE_12BIT             4095U    ///< 12位ADC最大值
#define ADC_MAX_VALUE_10BIT             1023U    ///< 10位ADC最大值
#define ADC_MAX_VALUE_8BIT              255U     ///< 8位ADC最大值
#define ADC_MAX_VALUE_6BIT              63U      ///< 6位ADC最大值

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup ADC_DMA_Exported_Macros ADC_DMA导出宏
  * @{
  */

/**
  * @brief  ADC原始值转换为电压(mV)
  */
#define ADC_TO_VOLTAGE_MV(adc_val, max_val) \
    ((uint32_t)(adc_val) * ADC_VREF_VOLTAGE_MV / (max_val))

/**
  * @brief  电压(mV)转换为ADC值
  */
#define VOLTAGE_MV_TO_ADC(voltage_mv, max_val) \
    ((uint16_t)((voltage_mv) * (max_val) / ADC_VREF_VOLTAGE_MV))

/**
  * @brief  检查缓冲区是否就绪
  */
#define ADC_IS_DATA_READY(handle)       ((handle)->buffer.data_ready)

/**
  * @brief  获取当前活动缓冲区
  */
#define ADC_GET_ACTIVE_BUFFER(handle)   ((handle)->buffer.active_buffer)

/**
  * @brief  获取当前数据缓冲区指针
  */
#define ADC_GET_CURRENT_BUFFER(handle)  \
    ((handle)->buffer.active_buffer ? (handle)->buffer.pong_buffer : (handle)->buffer.ping_buffer)

/**
  * @brief  获取非活动缓冲区指针
  */
#define ADC_GET_INACTIVE_BUFFER(handle) \
    ((handle)->buffer.active_buffer ? (handle)->buffer.ping_buffer : (handle)->buffer.pong_buffer)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup ADC_DMA_Exported_Variables ADC_DMA导出变量
  * @{
  */

extern ADC_Handle_t g_adc1_handle;              ///< ADC1句柄
extern volatile bool g_adc_conversion_complete; ///< 转换完成标志
extern volatile bool g_adc_dma_half_complete;   ///< DMA半传输完成标志
extern volatile bool g_adc_dma_full_complete;   ///< DMA全传输完成标志

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup ADC_DMA_Exported_Functions ADC_DMA导出函数
  * @{
  */

/**
  * @brief  ADC1+DMA初始化
  * @param  config: 配置参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t ADC1_DMA_Init(ADC_Config_t* config);

/**
  * @brief  ADC反初始化
  * @param  None
  * @retval None
  */
void ADC1_DMA_DeInit(void);

/**
  * @brief  开始ADC采集
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t ADC_Start_Acquisition(void);

/**
  * @brief  停止ADC采集
  * @param  None
  * @retval None
  */
void ADC_Stop_Acquisition(void);

/**
  * @brief  获取单通道ADC值
  * @param  channel: 通道号
  * @retval ADC值
  */
uint16_t ADC_GetChannelValue(ADC_Channel_t channel);

/**
  * @brief  获取单通道电压值(mV)
  * @param  channel: 通道号
  * @retval 电压值(mV)
  */
uint32_t ADC_GetChannelVoltage_mV(ADC_Channel_t channel);

/**
  * @brief  获取多通道数据
  * @param  data: 数据缓冲区
  * @param  length: 缓冲区长度
  * @retval 实际获取的数据长度
  */
uint32_t ADC_GetMultiChannelData(uint16_t* data, uint32_t length);

/**
  * @brief  处理ADC数据
  * @param  None
  * @retval None
  */
void ADC_ProcessData(void);

/**
  * @brief  ADC校准
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t ADC_Calibrate(void);

/**
  * @brief  设置采样率
  * @param  sample_rate: 采样率(Hz)
  * @retval 0: 成功, -1: 失败
  */
int8_t ADC_SetSampleRate(uint32_t sample_rate);

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void ADC_GetStats(ADC_Stats_t* stats);

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void ADC_ResetStats(void);

/**
  * @brief  ADC中断处理函数
  * @param  None
  * @retval None
  */
void ADC_IRQHandler(void);

/**
  * @brief  DMA2_Stream0中断处理函数
  * @param  None
  * @retval None
  */
void DMA2_Stream0_IRQHandler(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __ADC_DMA_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
