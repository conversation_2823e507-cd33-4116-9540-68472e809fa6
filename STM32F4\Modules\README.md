# STM32F4竞赛级模块库 - 硬件适配修正版

## 📋 概述

这是一套专为全国大学生电子设计竞赛设计的STM32F4标准外设库模块集合，经过严格的硬件适配修正，确保与实际硬件100%匹配，提供稳定可靠的基础驱动功能。

## 🎯 硬件平台

- **开发板**: 嘉立创"天空星"STM32F407VGT6
- **开发环境**: Keil MDK 5
- **软件库**: STM32F4标准外设库(SPL)
- **系统时钟**: 168MHz

## 📁 模块结构

```
STM32F4/Modules/
├── Core/                   # 核心基础模块
│   ├── systick.c/h        # 高精度SysTick延时
│   └── usart.c/h          # 高性能串口调试
├── Acquisition/           # 信号采集模块
│   ├── adc_dma.c/h        # ADC+DMA多通道采集
│   └── parallel_adc.c/h   # 高速并行ADC接口
├── Generation/            # 信号生成模块
│   └── dds_wavegen.c/h    # DDS波形生成
├── Processing/            # 信号处理模块
│   ├── fft.c/h            # FFT计算(需CMSIS-DSP)
│   ├── input_capture.c/h  # 频率测量
│   └── pwm.c/h            # PWM生成
└── Interface/             # 用户接口模块
    ├── oled.c/h           # OLED显示
    └── key.c/h            # 按键扫描
```

## 🚀 核心特性

### 1. 高精度SysTick延时模块
- ✅ DWT计数器微秒级精确延时
- ✅ 非阻塞延时接口
- ✅ 系统运行时间统计
- ✅ 稳定可靠的基础延时

### 2. 高性能串口调试模块
- ✅ DMA发送，避免阻塞
- ✅ 环形缓冲区管理
- ✅ printf重定向支持
- ✅ 二进制数据传输

### 3. ADC+DMA多通道采集模块
- ✅ 基础DMA数据传输
- ✅ 多通道连续采集
- ✅ 稳定的采集接口
- ✅ 保守的采样率设置

### 4. 14位并行ADC接口模块 (LTC2246)
- ✅ 14位并行数据接口 (硬件匹配)
- ✅ PC0-PC13数据线配置
- ✅ 外部时钟同步采集
- ✅ 基础数据缓冲管理

### 5. DDS波形生成模块
- ✅ 基于查找表的波形生成
- ✅ 多波形支持(正弦、方波、三角)
- ✅ 基础频率和幅度控制
- ✅ DAC+DMA+TIM稳定输出

### 6. I2C OLED显示模块 (SSD1306)
- ✅ 硬件I2C1接口 (PB6/PB7)
- ✅ 完整的SSD1306初始化
- ✅ 基础显示功能
- ✅ 字符、字符串、数字显示

### 7. 独立按键扫描模块
- ✅ 基础按键扫描 (PE4/PE3)
- ✅ 软件消抖
- ✅ 非阻塞式检测
- ✅ 简洁的API接口

## 🔧 硬件连接 - 严格匹配实际硬件

### ADC多通道采集
- **ADC1_IN1**: PA1 (通道1)
- **ADC1_IN2**: PA2 (通道2)
- **ADC1_IN3**: PA3 (通道3)

### 14位并行ADC接口 (LTC2246)
- **ADC型号**: LTC2246 (14位高速ADC)
- **数据线 D0-D13**: PC0-PC13 (14位数据)
- **时钟线 CLK_OUT**: PA0 (EXTI0)
- **数据有效 DV**: PA1 (可选)
- **溢出指示 OVR**: PA2 (可选)

### DDS波形输出
- **DAC1_OUT**: PA4 (模拟输出)

### I2C OLED显示 (SSD1306)
- **SCL**: PB6 (I2C1_SCL)
- **SDA**: PB7 (I2C1_SDA)
- **接口**: 4引脚I2C (VCC, GND, SCL, SDA)

### 独立按键
- **KEY0**: PE4 (低电平有效)
- **KEY1**: PE3 (低电平有效)

### 串口调试
- **USART1_TX**: PA9
- **USART1_RX**: PA10

## 📖 使用方法

### 1. 基本初始化

```c
#include "systick.h"
#include "usart.h"
#include "adc_dma.h"
#include "dds_wavegen.h"

int main(void)
{
    // 系统初始化
    SystemInit();
    
    // 初始化SysTick
    SysTick_Init();
    
    // 初始化串口
    USART1_Init(115200);
    
    // 初始化ADC
    ADC_Config_t adc_config = {
        .sample_rate = 100000,
        .resolution = ADC_RESOLUTION_12BIT,
        .oversample_ratio = 4,
        .enable_filter = true,
        .mode = ADC_MODE_CONTINUOUS
    };
    ADC1_DMA_Init(&adc_config);
    ADC_Start_Acquisition();
    
    // 初始化DDS
    DDS_Config_t dds_config = {
        .frequency = 1000,
        .amplitude = 2048,
        .wave_type = DDS_WAVE_SINE,
        .sample_rate = 1000000
    };
    DDS_Init(&dds_config);
    DDS_Start();
    
    while(1) {
        // 主循环处理
    }
}
```

### 2. ADC数据处理

```c
// 检查数据就绪
if (g_adc_dma_full_complete) {
    g_adc_dma_full_complete = false;
    
    // 处理ADC数据
    ADC_ProcessData();
    
    // 获取统计信息
    ADC_Stats_t stats;
    ADC_GetStats(&stats);
    
    printf("采样率: %.1f kSPS\n", stats.actual_sample_rate/1000.0f);
}
```

### 3. DDS波形控制

```c
// 设置频率
DDS_SetFrequency(2000); // 2kHz

// 切换波形
DDS_SetWaveType(DDS_WAVE_SQUARE);

// 设置幅度
DDS_SetAmplitude(3000); // 约2.4V幅度
```

## ⚙️ CMSIS-DSP库配置

要使用FFT模块，需要在Keil项目中进行以下配置：

### 1. 添加库路径
在项目设置的"C/C++"选项卡中，添加CMSIS-DSP库的Include路径：
```
..\..\Libraries\CMSIS\DSP\Include
```

### 2. 添加预定义宏
在"C/C++"选项卡的"Define"中添加：
```
ARM_MATH_CM4,ARM_MATH_MATRIX_CHECK,ARM_MATH_ROUNDING
```

### 3. 链接库文件
在"Linker"选项卡中添加库文件：
```
arm_cortexM4lf_math.lib
```

### 4. 启用FFT模块
取消注释main.c中的FFT相关代码：
```c
#include "fft.h"  // 取消注释
```

## 📊 性能指标

| 模块 | 性能指标 | 保守目标值 |
|------|----------|------------|
| ADC采集 | 稳定采样率 | 500KSPS |
| 并行ADC | 稳定采样率 | 10MSPS |
| DDS生成 | 基础频率控制 | 1Hz-50kHz |
| I2C OLED | 刷新速度 | 稳定显示 |
| 按键扫描 | 响应时间 | <50ms |
| 系统响应 | 中断响应时间 | <1ms |

## 🔍 调试和优化

### 1. 串口调试输出
```c
USART_Printf("调试信息: %d\n", value);
USART_DEBUG("调试级别信息");
USART_ERROR("错误信息");
```

### 2. 性能监控
```c
// 获取系统运行时间
uint32_t uptime = SysTick_GetUptime_ms();

// 获取ADC统计信息
ADC_Stats_t adc_stats;
ADC_GetStats(&adc_stats);

// 获取DDS统计信息
DDS_Stats_t dds_stats;
DDS_GetStats(&dds_stats);
```

### 3. 内存优化
- 使用DMA减少CPU占用
- 合理配置缓冲区大小
- 启用编译器优化(-O2)

## 🚨 注意事项

1. **时钟配置**: 确保系统时钟配置为168MHz
2. **中断优先级**: DMA中断设置为最高优先级
3. **内存对齐**: FFT缓冲区需要32字节对齐
4. **电源滤波**: 模拟部分需要良好的电源滤波
5. **PCB布局**: 数字和模拟部分分离布局

## 📞 技术支持

如有问题，请检查：
1. 硬件连接是否正确
2. 时钟配置是否正确
3. 库文件是否正确添加
4. 中断优先级是否合理

---

**版权所有 © 2024 嵌入式竞赛团队**
