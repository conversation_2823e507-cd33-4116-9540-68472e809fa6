/**
  ******************************************************************************
  * @file    oled.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024
  * @brief   I2C OLED显示模块实现 - 硬件适配修正版
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "oled.h"
#include "systick.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/** @defgroup OLED_Private_Defines OLED私有定义
  * @{
  */

#define OLED_I2C_PORT               GPIOB
#define OLED_I2C_CLK                RCC_AHB1Periph_GPIOB
#define OLED_I2C_SCL_PIN            GPIO_Pin_6   ///< PB6 - I2C1_SCL
#define OLED_I2C_SDA_PIN            GPIO_Pin_7   ///< PB7 - I2C1_SDA

#define OLED_I2C                    I2C1
#define OLED_I2C_RCC                RCC_APB1Periph_I2C1
#define OLED_I2C_SPEED              400000       ///< I2C速度400kHz

#define OLED_WRITE_CMD              0x00         ///< 写命令
#define OLED_WRITE_DATA             0x40         ///< 写数据

/**
  * @}
  */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/** @defgroup OLED_Private_Variables OLED私有变量
  * @{
  */

volatile bool g_oled_initialized = false;       ///< OLED初始化标志

// OLED显示缓冲区 (128x64/8 = 1024字节)
static uint8_t s_oled_buffer[OLED_WIDTH * OLED_PAGES];

// 6x8字体
static const uint8_t s_font_6x8[][6] = {
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, // 空格
    {0x00, 0x00, 0x00, 0x2f, 0x00, 0x00}, // !
    {0x00, 0x00, 0x07, 0x00, 0x07, 0x00}, // "
    {0x00, 0x14, 0x7f, 0x14, 0x7f, 0x14}, // #
    {0x00, 0x24, 0x2a, 0x7f, 0x2a, 0x12}, // $
    {0x00, 0x62, 0x64, 0x08, 0x13, 0x23}, // %
    {0x00, 0x36, 0x49, 0x55, 0x22, 0x50}, // &
    {0x00, 0x00, 0x05, 0x03, 0x00, 0x00}, // '
    {0x00, 0x00, 0x1c, 0x22, 0x41, 0x00}, // (
    {0x00, 0x00, 0x41, 0x22, 0x1c, 0x00}, // )
    {0x00, 0x14, 0x08, 0x3E, 0x08, 0x14}, // *
    {0x00, 0x08, 0x08, 0x3E, 0x08, 0x08}, // +
    {0x00, 0x00, 0x00, 0xA0, 0x60, 0x00}, // ,
    {0x00, 0x08, 0x08, 0x08, 0x08, 0x08}, // -
    {0x00, 0x00, 0x60, 0x60, 0x00, 0x00}, // .
    {0x00, 0x20, 0x10, 0x08, 0x04, 0x02}, // /
    {0x00, 0x3E, 0x51, 0x49, 0x45, 0x3E}, // 0
    {0x00, 0x00, 0x42, 0x7F, 0x40, 0x00}, // 1
    {0x00, 0x42, 0x61, 0x51, 0x49, 0x46}, // 2
    {0x00, 0x21, 0x41, 0x45, 0x4B, 0x31}, // 3
    {0x00, 0x18, 0x14, 0x12, 0x7F, 0x10}, // 4
    {0x00, 0x27, 0x45, 0x45, 0x45, 0x39}, // 5
    {0x00, 0x3C, 0x4A, 0x49, 0x49, 0x30}, // 6
    {0x00, 0x01, 0x71, 0x09, 0x05, 0x03}, // 7
    {0x00, 0x36, 0x49, 0x49, 0x49, 0x36}, // 8
    {0x00, 0x06, 0x49, 0x49, 0x29, 0x1E}, // 9
    {0x00, 0x00, 0x36, 0x36, 0x00, 0x00}, // :
    {0x00, 0x00, 0x56, 0x36, 0x00, 0x00}, // ;
    {0x00, 0x08, 0x14, 0x22, 0x41, 0x00}, // <
    {0x00, 0x14, 0x14, 0x14, 0x14, 0x14}, // =
    {0x00, 0x00, 0x41, 0x22, 0x14, 0x08}, // >
    {0x00, 0x02, 0x01, 0x51, 0x09, 0x06}, // ?
    {0x00, 0x32, 0x49, 0x59, 0x51, 0x3E}, // @
    {0x00, 0x7C, 0x12, 0x11, 0x12, 0x7C}, // A
    {0x00, 0x7F, 0x49, 0x49, 0x49, 0x36}, // B
    {0x00, 0x3E, 0x41, 0x41, 0x41, 0x22}, // C
    {0x00, 0x7F, 0x41, 0x41, 0x22, 0x1C}, // D
    {0x00, 0x7F, 0x49, 0x49, 0x49, 0x41}, // E
    {0x00, 0x7F, 0x09, 0x09, 0x09, 0x01}, // F
    {0x00, 0x3E, 0x41, 0x49, 0x49, 0x7A}, // G
    {0x00, 0x7F, 0x08, 0x08, 0x08, 0x7F}, // H
    {0x00, 0x00, 0x41, 0x7F, 0x41, 0x00}, // I
    {0x00, 0x20, 0x40, 0x41, 0x3F, 0x01}, // J
    {0x00, 0x7F, 0x08, 0x14, 0x22, 0x41}, // K
    {0x00, 0x7F, 0x40, 0x40, 0x40, 0x40}, // L
    {0x00, 0x7F, 0x02, 0x0C, 0x02, 0x7F}, // M
    {0x00, 0x7F, 0x04, 0x08, 0x10, 0x7F}, // N
    {0x00, 0x3E, 0x41, 0x41, 0x41, 0x3E}, // O
    {0x00, 0x7F, 0x09, 0x09, 0x09, 0x06}, // P
    {0x00, 0x3E, 0x41, 0x51, 0x21, 0x5E}, // Q
    {0x00, 0x7F, 0x09, 0x19, 0x29, 0x46}, // R
    {0x00, 0x46, 0x49, 0x49, 0x49, 0x31}, // S
    {0x00, 0x01, 0x01, 0x7F, 0x01, 0x01}, // T
    {0x00, 0x3F, 0x40, 0x40, 0x40, 0x3F}, // U
    {0x00, 0x1F, 0x20, 0x40, 0x20, 0x1F}, // V
    {0x00, 0x3F, 0x40, 0x38, 0x40, 0x3F}, // W
    {0x00, 0x63, 0x14, 0x08, 0x14, 0x63}, // X
    {0x00, 0x07, 0x08, 0x70, 0x08, 0x07}, // Y
    {0x00, 0x61, 0x51, 0x49, 0x45, 0x43}, // Z
};

/**
  * @}
  */

/* Private function prototypes -----------------------------------------------*/
static void OLED_I2C_Config(void);
static int8_t OLED_I2C_WriteCmd(uint8_t cmd);
static int8_t OLED_I2C_WriteData(uint8_t data);
static int8_t OLED_I2C_WriteBuffer(const uint8_t* buffer, uint16_t size);
static void OLED_SetPos(uint8_t x, uint8_t y);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  OLED I2C配置
  * @param  None
  * @retval None
  */
static void OLED_I2C_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    I2C_InitTypeDef I2C_InitStructure;
    
    // 使能GPIO和I2C时钟
    RCC_AHB1PeriphClockCmd(OLED_I2C_CLK, ENABLE);
    RCC_APB1PeriphClockCmd(OLED_I2C_RCC, ENABLE);
    
    // 配置I2C引脚
    GPIO_PinAFConfig(OLED_I2C_PORT, GPIO_PinSource6, GPIO_AF_I2C1);
    GPIO_PinAFConfig(OLED_I2C_PORT, GPIO_PinSource7, GPIO_AF_I2C1);
    
    GPIO_InitStructure.GPIO_Pin = OLED_I2C_SCL_PIN | OLED_I2C_SDA_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(OLED_I2C_PORT, &GPIO_InitStructure);
    
    // 复位I2C
    I2C_DeInit(OLED_I2C);
    
    // 配置I2C
    I2C_InitStructure.I2C_Mode = I2C_Mode_I2C;
    I2C_InitStructure.I2C_DutyCycle = I2C_DutyCycle_2;
    I2C_InitStructure.I2C_OwnAddress1 = 0x00;
    I2C_InitStructure.I2C_Ack = I2C_Ack_Enable;
    I2C_InitStructure.I2C_AcknowledgedAddress = I2C_AcknowledgedAddress_7bit;
    I2C_InitStructure.I2C_ClockSpeed = OLED_I2C_SPEED;
    I2C_Init(OLED_I2C, &I2C_InitStructure);
    
    // 使能I2C
    I2C_Cmd(OLED_I2C, ENABLE);
}

/**
  * @brief  I2C写命令
  * @param  cmd: 命令字节
  * @retval 0: 成功, -1: 失败
  */
static int8_t OLED_I2C_WriteCmd(uint8_t cmd)
{
    uint32_t timeout = OLED_I2C_TIMEOUT;
    
    // 等待I2C空闲
    while (I2C_GetFlagStatus(OLED_I2C, I2C_FLAG_BUSY)) {
        if (--timeout == 0) return -1;
    }
    
    // 发送起始信号
    I2C_GenerateSTART(OLED_I2C, ENABLE);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_MODE_SELECT)) {
        if (--timeout == 0) return -1;
    }
    
    // 发送设备地址
    I2C_Send7bitAddress(OLED_I2C, OLED_I2C_ADDRESS, I2C_Direction_Transmitter);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_TRANSMITTER_MODE_SELECTED)) {
        if (--timeout == 0) return -1;
    }
    
    // 发送控制字节(命令)
    I2C_SendData(OLED_I2C, OLED_WRITE_CMD);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED)) {
        if (--timeout == 0) return -1;
    }
    
    // 发送命令字节
    I2C_SendData(OLED_I2C, cmd);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED)) {
        if (--timeout == 0) return -1;
    }
    
    // 发送停止信号
    I2C_GenerateSTOP(OLED_I2C, ENABLE);
    
    return 0;
}

/**
  * @brief  I2C写数据
  * @param  data: 数据字节
  * @retval 0: 成功, -1: 失败
  */
static int8_t OLED_I2C_WriteData(uint8_t data)
{
    uint32_t timeout = OLED_I2C_TIMEOUT;
    
    // 等待I2C空闲
    while (I2C_GetFlagStatus(OLED_I2C, I2C_FLAG_BUSY)) {
        if (--timeout == 0) return -1;
    }
    
    // 发送起始信号
    I2C_GenerateSTART(OLED_I2C, ENABLE);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_MODE_SELECT)) {
        if (--timeout == 0) return -1;
    }
    
    // 发送设备地址
    I2C_Send7bitAddress(OLED_I2C, OLED_I2C_ADDRESS, I2C_Direction_Transmitter);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_TRANSMITTER_MODE_SELECTED)) {
        if (--timeout == 0) return -1;
    }
    
    // 发送控制字节(数据)
    I2C_SendData(OLED_I2C, OLED_WRITE_DATA);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED)) {
        if (--timeout == 0) return -1;
    }
    
    // 发送数据字节
    I2C_SendData(OLED_I2C, data);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED)) {
        if (--timeout == 0) return -1;
    }
    
    // 发送停止信号
    I2C_GenerateSTOP(OLED_I2C, ENABLE);
    
    return 0;
}

/**
  * @brief  I2C写缓冲区数据
  * @param  buffer: 数据缓冲区
  * @param  size: 数据大小
  * @retval 0: 成功, -1: 失败
  */
static int8_t OLED_I2C_WriteBuffer(const uint8_t* buffer, uint16_t size)
{
    uint32_t timeout = OLED_I2C_TIMEOUT;

    if (buffer == NULL || size == 0) return -1;

    // 等待I2C空闲
    while (I2C_GetFlagStatus(OLED_I2C, I2C_FLAG_BUSY)) {
        if (--timeout == 0) return -1;
    }

    // 发送起始信号
    I2C_GenerateSTART(OLED_I2C, ENABLE);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_MODE_SELECT)) {
        if (--timeout == 0) return -1;
    }

    // 发送设备地址
    I2C_Send7bitAddress(OLED_I2C, OLED_I2C_ADDRESS, I2C_Direction_Transmitter);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_TRANSMITTER_MODE_SELECTED)) {
        if (--timeout == 0) return -1;
    }

    // 发送控制字节(数据)
    I2C_SendData(OLED_I2C, OLED_WRITE_DATA);
    timeout = OLED_I2C_TIMEOUT;
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED)) {
        if (--timeout == 0) return -1;
    }

    // 发送数据
    for (uint16_t i = 0; i < size; i++) {
        I2C_SendData(OLED_I2C, buffer[i]);
        timeout = OLED_I2C_TIMEOUT;
        while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED)) {
            if (--timeout == 0) return -1;
        }
    }

    // 发送停止信号
    I2C_GenerateSTOP(OLED_I2C, ENABLE);

    return 0;
}

/**
  * @brief  设置显示位置
  * @param  x: X坐标
  * @param  y: Y坐标(页)
  * @retval None
  */
static void OLED_SetPos(uint8_t x, uint8_t y)
{
    OLED_I2C_WriteCmd(0xB0 + y);                    // 设置页地址
    OLED_I2C_WriteCmd(((x & 0xF0) >> 4) | 0x10);   // 设置列地址高4位
    OLED_I2C_WriteCmd((x & 0x0F) | 0x00);          // 设置列地址低4位
}

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  OLED初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_Init(void)
{
    // 配置I2C
    OLED_I2C_Config();

    // 延时等待OLED稳定
    Delay_ms(100);

    // SSD1306初始化序列
    if (OLED_I2C_WriteCmd(OLED_CMD_DISPLAY_OFF) != 0) return -1;        // 关闭显示
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_CLOCK_DIV) != 0) return -1;      // 设置时钟分频
    if (OLED_I2C_WriteCmd(0x80) != 0) return -1;                       // 分频因子
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_MUX_RATIO) != 0) return -1;      // 设置复用比
    if (OLED_I2C_WriteCmd(0x3F) != 0) return -1;                       // 64MUX
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_DISPLAY_OFFSET) != 0) return -1; // 设置显示偏移
    if (OLED_I2C_WriteCmd(0x00) != 0) return -1;                       // 无偏移
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_START_LINE | 0x00) != 0) return -1; // 设置起始行
    if (OLED_I2C_WriteCmd(OLED_CMD_CHARGE_PUMP) != 0) return -1;        // 电荷泵设置
    if (OLED_I2C_WriteCmd(0x14) != 0) return -1;                       // 使能电荷泵
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_MEMORY_MODE) != 0) return -1;    // 设置内存地址模式
    if (OLED_I2C_WriteCmd(0x00) != 0) return -1;                       // 水平地址模式
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_SEGMENT_REMAP | 0x01) != 0) return -1; // 段重映射
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_COM_SCAN_DIR) != 0) return -1;   // COM扫描方向
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_COM_PINS) != 0) return -1;       // COM引脚配置
    if (OLED_I2C_WriteCmd(0x12) != 0) return -1;                       // 交替COM引脚配置
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_CONTRAST) != 0) return -1;       // 设置对比度
    if (OLED_I2C_WriteCmd(0xCF) != 0) return -1;                       // 对比度值
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_PRECHARGE) != 0) return -1;      // 设置预充电周期
    if (OLED_I2C_WriteCmd(0xF1) != 0) return -1;                       // 预充电周期
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_VCOM_DETECT) != 0) return -1;    // 设置VCOM检测电压
    if (OLED_I2C_WriteCmd(0x40) != 0) return -1;                       // VCOM检测电压
    if (OLED_I2C_WriteCmd(OLED_CMD_DISPLAY_ALL_ON) != 0) return -1;     // 全屏点亮
    if (OLED_I2C_WriteCmd(OLED_CMD_DISPLAY_NORMAL) != 0) return -1;     // 正常显示
    if (OLED_I2C_WriteCmd(OLED_CMD_DISPLAY_ON) != 0) return -1;         // 开启显示

    // 清屏
    OLED_Clear();

    g_oled_initialized = true;

    return 0;
}

/**
  * @brief  清屏
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_Clear(void)
{
    if (!g_oled_initialized) return -1;

    // 清空缓冲区
    memset(s_oled_buffer, 0x00, sizeof(s_oled_buffer));

    // 刷新到屏幕
    return OLED_Refresh();
}

/**
  * @brief  刷新显示
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_Refresh(void)
{
    if (!g_oled_initialized) return -1;

    // 设置列地址范围
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_COLUMN_ADDR) != 0) return -1;
    if (OLED_I2C_WriteCmd(0x00) != 0) return -1;  // 起始列
    if (OLED_I2C_WriteCmd(0x7F) != 0) return -1;  // 结束列

    // 设置页地址范围
    if (OLED_I2C_WriteCmd(OLED_CMD_SET_PAGE_ADDR) != 0) return -1;
    if (OLED_I2C_WriteCmd(0x00) != 0) return -1;  // 起始页
    if (OLED_I2C_WriteCmd(0x07) != 0) return -1;  // 结束页

    // 发送整个缓冲区
    return OLED_I2C_WriteBuffer(s_oled_buffer, sizeof(s_oled_buffer));
}

/**
  * @brief  显示单个字符
  * @param  x: X坐标(0-127)
  * @param  y: Y坐标(0-63)
  * @param  chr: 要显示的字符
  * @param  font_size: 字体大小
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_ShowChar(uint8_t x, uint8_t y, char chr, OLED_FontSize_t font_size)
{
    if (!g_oled_initialized) return -1;
    if (!OLED_IS_VALID_X(x) || !OLED_IS_VALID_Y(y)) return -1;

    uint8_t c = chr - ' '; // 转换为字体表索引
    if (c > 94) return -1; // 超出字体表范围

    if (font_size == OLED_FONT_6x8) {
        uint8_t page = OLED_Y_TO_PAGE(y);
        if (page >= OLED_PAGES || x + 6 > OLED_WIDTH) return -1;

        // 写入6x8字体数据到缓冲区
        for (uint8_t i = 0; i < 6; i++) {
            if (x + i < OLED_WIDTH) {
                s_oled_buffer[page * OLED_WIDTH + x + i] = s_font_6x8[c][i];
            }
        }
    }

    return 0;
}

/**
  * @brief  显示字符串
  * @param  x: X坐标(0-127)
  * @param  y: Y坐标(0-63)
  * @param  str: 要显示的字符串
  * @param  font_size: 字体大小
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_ShowString(uint8_t x, uint8_t y, const char* str, OLED_FontSize_t font_size)
{
    if (!g_oled_initialized || str == NULL) return -1;

    uint8_t start_x = x;

    while (*str != '\0') {
        if (OLED_ShowChar(x, y, *str, font_size) != 0) {
            break; // 超出显示范围
        }

        str++;
        if (font_size == OLED_FONT_6x8) {
            x += 6;
            if (x >= OLED_WIDTH) {
                x = start_x;
                y += 8;
                if (y >= OLED_HEIGHT) break;
            }
        }
    }

    return 0;
}

/**
  * @brief  显示数字
  * @param  x: X坐标(0-127)
  * @param  y: Y坐标(0-63)
  * @param  num: 要显示的数字
  * @param  len: 显示长度
  * @param  font_size: 字体大小
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, OLED_FontSize_t font_size)
{
    if (!g_oled_initialized) return -1;

    char num_str[12]; // 足够存储32位数字
    uint8_t i = 0;

    // 转换数字为字符串
    if (num == 0) {
        num_str[i++] = '0';
    } else {
        uint32_t temp = num;
        uint8_t digits = 0;

        // 计算位数
        while (temp > 0) {
            temp /= 10;
            digits++;
        }

        // 填充前导零
        for (uint8_t j = 0; j < len - digits; j++) {
            num_str[i++] = '0';
        }

        // 转换数字
        temp = num;
        for (uint8_t j = digits; j > 0; j--) {
            num_str[i + j - 1] = (temp % 10) + '0';
            temp /= 10;
        }
        i += digits;
    }

    num_str[i] = '\0';

    return OLED_ShowString(x, y, num_str, font_size);
}

/**
  * @brief  显示浮点数
  * @param  x: X坐标(0-127)
  * @param  y: Y坐标(0-63)
  * @param  f_num: 要显示的浮点数
  * @param  int_len: 整数部分长度
  * @param  frac_len: 小数部分长度
  * @param  font_size: 字体大小
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_ShowFloat(uint8_t x, uint8_t y, float f_num, uint8_t int_len, uint8_t frac_len, OLED_FontSize_t font_size)
{
    if (!g_oled_initialized) return -1;

    char float_str[20];
    int32_t int_part = (int32_t)f_num;
    int32_t frac_part = (int32_t)((f_num - int_part) * 1000); // 保留3位小数

    if (frac_part < 0) frac_part = -frac_part;

    // 格式化字符串
    sprintf(float_str, "%*ld.%0*ld", int_len, int_part, frac_len, frac_part);

    return OLED_ShowString(x, y, float_str, font_size);
}

/**
  * @brief  在指定位置画点
  * @param  x: X坐标(0-127)
  * @param  y: Y坐标(0-63)
  * @param  dot: 点状态(0-清除, 1-点亮)
  * @retval 0: 成功, -1: 失败
  */
int8_t OLED_DrawPoint(uint8_t x, uint8_t y, uint8_t dot)
{
    if (!g_oled_initialized) return -1;
    if (!OLED_IS_VALID_X(x) || !OLED_IS_VALID_Y(y)) return -1;

    uint8_t page = OLED_Y_TO_PAGE(y);
    uint8_t bit = OLED_Y_TO_BIT(y);
    uint16_t pos = page * OLED_WIDTH + x;

    if (dot) {
        s_oled_buffer[pos] |= (1 << bit);   // 点亮
    } else {
        s_oled_buffer[pos] &= ~(1 << bit);  // 清除
    }

    return 0;
}

/**
  * @brief  检查OLED是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool OLED_IsReady(void)
{
    return g_oled_initialized;
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
